{"metadata": {"timestamp": "2025-07-08T05:42:38.282207", "total_files": 25, "total_links": 496, "broken_links": 228, "success_rate": 54.03225806451613, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": 2}, "new_issues": [{"file": "030-laravel-telescope-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "030-laravel-telescope-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "030-laravel-telescope-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "030-laravel-telescope-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "030-laravel-telescope-guide.md", "link": "#authorization--security", "text": "Authorization & Security", "status": "Anchor not found: #authorization--security"}, {"file": "030-laravel-telescope-guide.md", "link": "#21-gate-based-authorization", "text": "2.1. Gate-Based Authorization", "status": "Anchor not found: #21-gate-based-authorization"}, {"file": "030-laravel-telescope-guide.md", "link": "#22-environment-specific-access", "text": "2.2. Environment-Specific Access", "status": "Anchor not found: #22-environment-specific-access"}, {"file": "030-laravel-telescope-guide.md", "link": "#23-ip-whitelisting", "text": "2.3. IP Whitelisting", "status": "Anchor not found: #23-ip-whitelisting"}, {"file": "030-laravel-telescope-guide.md", "link": "#31-watcher-configuration", "text": "3.1. Watcher Configuration", "status": "Anchor not found: #31-watcher-configuration"}, {"file": "030-laravel-telescope-guide.md", "link": "#32-filtering--sampling", "text": "3.2. Fi<PERSON><PERSON> & Sam<PERSON>", "status": "Anchor not found: #32-filtering--sampling"}, {"file": "030-laravel-telescope-guide.md", "link": "#33-performance-impact-mitigation", "text": "3.3. Performance Impact Mitigation", "status": "Anchor not found: #33-performance-impact-mitigation"}, {"file": "030-laravel-telescope-guide.md", "link": "#data-pruning--storage-management", "text": "Data Pruning & Storage Management", "status": "Anchor not found: #data-pruning--storage-management"}, {"file": "030-laravel-telescope-guide.md", "link": "#41-automated-pruning", "text": "4.1. Automated Pruning", "status": "Anchor not found: #41-automated-pruning"}, {"file": "030-laravel-telescope-guide.md", "link": "#42-storage-optimization", "text": "4.2. Storage Optimization", "status": "Anchor not found: #42-storage-optimization"}, {"file": "030-laravel-telescope-guide.md", "link": "#43-custom-retention-policies", "text": "4.3. Custom Retention Policies", "status": "Anchor not found: #43-custom-retention-policies"}, {"file": "130-spatie-laravel-settings-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "130-spatie-laravel-settings-guide.md", "link": "120-nn<PERSON><PERSON>-world-guide.md", "text": "NNJeim World Guide", "status": "File not found: 120-nnjeim-world-guide.md"}, {"file": "100-spatie-media-library-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "100-spatie-media-library-guide.md", "link": "#file-conversions--processing", "text": "File Conversions & Processing", "status": "Anchor not found: #file-conversions--processing"}, {"file": "100-spatie-media-library-guide.md", "link": "../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}, {"file": "050-laravel-horizon-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "050-laravel-horizon-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "050-laravel-horizon-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "050-laravel-horizon-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "050-laravel-horizon-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "050-laravel-horizon-guide.md", "link": "#22-authentication--authorization", "text": "2.2. Authentication & Authorization", "status": "Anchor not found: #22-authentication--authorization"}, {"file": "050-laravel-horizon-guide.md", "link": "#23-custom-dashboard-views", "text": "2.3. Custom Dashboard Views", "status": "Anchor not found: #23-custom-dashboard-views"}, {"file": "050-laravel-horizon-guide.md", "link": "#31-queue-worker-settings", "text": "3.1. <PERSON><PERSON> Worker Settings", "status": "Anchor not found: #31-queue-worker-settings"}, {"file": "050-laravel-horizon-guide.md", "link": "#32-supervisor-configuration", "text": "3.2. Supervisor Configuration", "status": "Anchor not found: #32-supervisor-configuration"}, {"file": "050-laravel-horizon-guide.md", "link": "#33-auto-scaling-setup", "text": "3.3. Auto-Scaling Setup", "status": "Anchor not found: #33-auto-scaling-setup"}, {"file": "050-laravel-horizon-guide.md", "link": "#41-horizon-watcher-integration", "text": "4.1. Horizon Watcher Integration", "status": "Anchor not found: #41-horizon-watcher-integration"}, {"file": "050-laravel-horizon-guide.md", "link": "#42-custom-metrics-collection", "text": "4.2. Custom Metrics Collection", "status": "Anchor not found: #42-custom-metrics-collection"}, {"file": "050-laravel-horizon-guide.md", "link": "#43-alert-configuration", "text": "4.3. <PERSON><PERSON>gu<PERSON>", "status": "Anchor not found: #43-alert-configuration"}, {"file": "050-laravel-horizon-guide.md", "link": "#performance-tuning", "text": "Performance Tuning", "status": "Anchor not found: #performance-tuning"}, {"file": "050-laravel-horizon-guide.md", "link": "#integration-strategies", "text": "Integration Strategies", "status": "Anchor not found: #integration-strategies"}, {"file": "050-laravel-horizon-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "050-laravel-horizon-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "000-packages-index.md", "link": "#backup--monitoring", "text": "Backup & Monitoring", "status": "Anchor not found: #backup--monitoring"}, {"file": "000-packages-index.md", "link": "#performance--optimization", "text": "Performance & Optimization", "status": "Anchor not found: #performance--optimization"}, {"file": "000-packages-index.md", "link": "#1-<PERSON><PERSON><PERSON>-backup", "text": "1. <PERSON><PERSON>", "status": "Anchor not found: #1-la<PERSON>l-backup"}, {"file": "000-packages-index.md", "link": "#2-laravel-pulse", "text": "2. <PERSON><PERSON>", "status": "Anchor not found: #2-laravel-pulse"}, {"file": "000-packages-index.md", "link": "#3-laravel-telescope", "text": "3. <PERSON><PERSON> Teles<PERSON>", "status": "Anchor not found: #3-la<PERSON>l-telescope"}, {"file": "000-packages-index.md", "link": "#4-laravel-octane-with-frankenphp", "text": "4. <PERSON><PERSON> with FrankenPHP", "status": "Anchor not found: #4-laravel-octane-with-frankenphp"}, {"file": "000-packages-index.md", "link": "#5-laravel-horizon", "text": "5. <PERSON><PERSON>", "status": "Anchor not found: #5-laravel-horizon"}, {"file": "000-packages-index.md", "link": "#6-laravel-data", "text": "6. <PERSON><PERSON>", "status": "Anchor not found: #6-laravel-data"}, {"file": "000-packages-index.md", "link": "#7-laravel-fractal", "text": "7. <PERSON><PERSON>", "status": "Anchor not found: #7-laravel-fractal"}, {"file": "000-packages-index.md", "link": "#8-laravel-sanctum", "text": "8. <PERSON><PERSON>", "status": "Anchor not found: #8-laravel-sanctum"}, {"file": "000-packages-index.md", "link": "#9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "text": "9. <PERSON><PERSON>", "status": "Anchor not found: #9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"file": "000-packages-index.md", "link": "#10-laravel-query-builder", "text": "10. <PERSON><PERSON> Query Builder", "status": "Anchor not found: #10-laravel-query-builder"}, {"file": "000-packages-index.md", "link": "#11-spatie-comments", "text": "11. <PERSON><PERSON>", "status": "Anchor not found: #11-spatie-comments"}, {"file": "000-packages-index.md", "link": "#12-laravel-folio", "text": "12. <PERSON><PERSON>", "status": "Anchor not found: #12-laravel-folio"}, {"file": "000-packages-index.md", "link": "#13-nn<PERSON><PERSON>-world", "text": "13. NNJeim World", "status": "Anchor not found: #13-nn<PERSON><PERSON>-world"}, {"file": "000-packages-index.md", "link": "#14-laravel-database-optimization", "text": "14. Laravel Database Optimization", "status": "Anchor not found: #14-laravel-database-optimization"}, {"file": "000-packages-index.md", "link": "#15-enhanced-spatie-activitylog", "text": "15. Enhanced Spatie ActivityLog", "status": "Anchor not found: #15-enhanced-spatie-activitylog"}, {"file": "000-packages-index.md", "link": "140-laravel-database-optimization-guide.md", "text": "Laravel Database Optimization Guide", "status": "File not found: 140-laravel-database-optimization-guide.md"}, {"file": "000-packages-index.md", "link": "150-enhanced-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: 150-enhanced-spatie-activitylog-guide.md"}, {"file": "000-packages-index.md", "link": "../070-chinook-hierarchy-comparison-guide.md", "text": "Chinook Hierarchy Comparison Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../070-chinook-hierarchy-comparison-guide.md"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#query-performance-monitoring", "text": "Query Performance Monitoring", "status": "Anchor not found: #query-performance-monitoring"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#sqlite-specific-optimizations", "text": "SQLite-Specific Optimizations", "status": "Anchor not found: #sqlite-specific-optimizations"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#performance-testing", "text": "Performance Testing", "status": "Anchor not found: #performance-testing"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#production-monitoring", "text": "Production Monitoring", "status": "Anchor not found: #production-monitoring"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "141-laravel-optimize-database-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "141-laravel-optimize-database-guide.md", "link": "150-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: 150-spatie-activitylog-guide.md"}, {"file": "141-laravel-optimize-database-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../testing/010-pest-testing-guide.md"}, {"file": "141-laravel-optimize-database-guide.md", "link": "090-laravel-workos-guide.md", "text": "Laravel WorkOS Guide", "status": "File not found: 090-laravel-workos-guide.md"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#21-local-storage-setup", "text": "2.1. Local Storage Setup", "status": "Anchor not found: #21-local-storage-setup"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#22-aws-s3-configuration", "text": "2.2. AWS S3 Configuration", "status": "Anchor not found: #22-aws-s3-configuration"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#23-google-cloud-storage", "text": "2.3. Google Cloud Storage", "status": "Anchor not found: #23-google-cloud-storage"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#24-multi-destination-strategy", "text": "2.4. Multi-Destination Strategy", "status": "Anchor not found: #24-multi-destination-strategy"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#31-basic-backup-settings", "text": "3.1. Basic Backup Settings", "status": "Anchor not found: #31-basic-backup-settings"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#32-database-backup-options", "text": "3.2. Database Backup Options", "status": "Anchor not found: #32-database-backup-options"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#33-file-backup-configuration", "text": "3.3. File Backup Configuration", "status": "Anchor not found: #33-file-backup-configuration"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#34-exclusion-patterns", "text": "3.4. <PERSON><PERSON><PERSON><PERSON>", "status": "Anchor not found: #34-exclusion-patterns"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#41-laravel-task-scheduler", "text": "4.1. <PERSON><PERSON> Scheduler", "status": "Anchor not found: #41-laravel-task-scheduler"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#42-cron-configuration", "text": "4.2. <PERSON>ron Configuration", "status": "Anchor not found: #42-cron-configuration"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#43-backup-frequency-strategies", "text": "4.3. <PERSON><PERSON> Frequency Strategies", "status": "Anchor not found: #43-backup-frequency-strategies"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#51-email-notifications", "text": "5.1. Email Notifications", "status": "Anchor not found: #51-email-notifications"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#52-slack-integration", "text": "5.2. <PERSON><PERSON><PERSON>", "status": "Anchor not found: #52-slack-integration"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#53-custom-notification-channels", "text": "5.3. Custom Notification Channels", "status": "Anchor not found: #53-custom-notification-channels"}, {"file": "010-la<PERSON>l-backup-guide.md", "link": "#monitoring--health-checks", "text": "Monitoring & Health Checks", "status": "Anchor not found: #monitoring--health-checks"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#12-frankenphp-server-setup", "text": "1.2. FrankenPHP Server Setup", "status": "Anchor not found: #12-frankenphp-server-setup"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#13-environment-configuration", "text": "1.3. Environment Configuration", "status": "Anchor not found: #13-environment-configuration"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#21-basic-server-settings", "text": "2.1. Basic Server Settings", "status": "Anchor not found: #21-basic-server-settings"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#22-performance-optimization", "text": "2.2. Performance Optimization", "status": "Anchor not found: #22-performance-optimization"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#23-ssltls-configuration", "text": "2.3. SSL/TLS Configuration", "status": "Anchor not found: #23-ssltls-configuration"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#31-memory-leak-prevention", "text": "3.1. Memory Leak Prevention", "status": "Anchor not found: #31-memory-leak-prevention"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#32-resource-optimization", "text": "3.2. Resource Optimization", "status": "Anchor not found: #32-resource-optimization"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#33-garbage-collection-tuning", "text": "3.3. Garbage Collection Tuning", "status": "Anchor not found: #33-garbage-collection-tuning"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#41-docker-configuration", "text": "4.1. <PERSON><PERSON> Configuration", "status": "Anchor not found: #41-docker-configuration"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#42-load-balancing", "text": "4.2. <PERSON><PERSON>", "status": "Anchor not found: #42-load-balancing"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#43-scaling-strategies", "text": "4.3. Scaling Strategies", "status": "Anchor not found: #43-scaling-strategies"}, {"file": "040-laravel-octane-frankenphp-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "120-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "120-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "120-spatie-activitylog-guide.md", "link": "../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}, {"file": "070-laravel-fractal-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "070-laravel-fractal-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "070-laravel-fractal-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "070-laravel-fractal-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "070-laravel-fractal-guide.md", "link": "#21-basic-transformers", "text": "2.1. Basic Transformers", "status": "Anchor not found: #21-basic-transformers"}, {"file": "070-laravel-fractal-guide.md", "link": "#22-advanced-transformers", "text": "2.2. Advanced Transformers", "status": "Anchor not found: #22-advanced-transformers"}, {"file": "070-laravel-fractal-guide.md", "link": "#23-nested-transformers", "text": "2.3. Nested Transformers", "status": "Anchor not found: #23-nested-transformers"}, {"file": "070-laravel-fractal-guide.md", "link": "#31-item-resources", "text": "3.1. Item Resources", "status": "Anchor not found: #31-item-resources"}, {"file": "070-laravel-fractal-guide.md", "link": "#32-collection-resources", "text": "3.2. Collection Resources", "status": "Anchor not found: #32-collection-resources"}, {"file": "070-laravel-fractal-guide.md", "link": "#33-relationship-handling", "text": "3.3. Relationship Handling", "status": "Anchor not found: #33-relationship-handling"}, {"file": "070-laravel-fractal-guide.md", "link": "#pagination--filtering", "text": "Pagination & Filtering", "status": "Anchor not found: #pagination--filtering"}, {"file": "070-laravel-fractal-guide.md", "link": "#41-pagination-setup", "text": "4.1. Pa<PERSON>ation Setup", "status": "Anchor not found: #41-pagination-setup"}, {"file": "070-laravel-fractal-guide.md", "link": "#42-advanced-filtering", "text": "4.2. Advanced Filtering", "status": "Anchor not found: #42-advanced-filtering"}, {"file": "070-laravel-fractal-guide.md", "link": "#43-sorting--ordering", "text": "4.3. Sorting & Ordering", "status": "Anchor not found: #43-sorting--ordering"}, {"file": "070-laravel-fractal-guide.md", "link": "#api-response-formatting", "text": "API Response Formatting", "status": "Anchor not found: #api-response-formatting"}, {"file": "070-laravel-fractal-guide.md", "link": "#caching-integration", "text": "Caching Integration", "status": "Anchor not found: #caching-integration"}, {"file": "070-laravel-fractal-guide.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "070-laravel-fractal-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "070-laravel-fractal-guide.md", "link": "#best-practices", "text": "Best Practices", "status": "Anchor not found: #best-practices"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "#country-state--city-management", "text": "Country, State & City Management", "status": "Anchor not found: #country-state--city-management"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "#user-profile-integration", "text": "User Profile Integration", "status": "Anchor not found: #user-profile-integration"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "#business-logic-integration", "text": "Business Logic Integration", "status": "Anchor not found: #business-logic-integration"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "140-laravel-optimize-database-guide.md", "text": "Laravel Database Optimization Guide", "status": "File not found: 140-laravel-optimize-database-guide.md"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "090-laravel-workos-guide.md", "text": "Laravel WorkOS Guide", "status": "File not found: 090-laravel-workos-guide.md"}, {"file": "131-nn<PERSON><PERSON>-world-guide.md", "link": "100-laravel-query-builder-guide.md", "text": "Laravel Query Builder Guide", "status": "File not found: 100-laravel-query-builder-guide.md"}, {"file": "090-spatie-tags-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "090-spatie-tags-guide.md", "link": "../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}, {"file": "150-spatie-laravel-translatable-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "150-spatie-laravel-translatable-guide.md", "link": "../integration-patterns.md", "text": "Package Integration Patterns", "status": "Path outside base directory: .ai/guides/chinook/packages/../integration-patterns.md"}, {"file": "110-spatie-permission-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "110-spatie-permission-guide.md", "link": "../010-chinook-models-guide.md", "text": "Chinook Models Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}, {"file": "080-laravel-sanctum-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "080-laravel-sanctum-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "080-laravel-sanctum-guide.md", "link": "#12-api-installation", "text": "1.2. API Installation", "status": "Anchor not found: #12-api-installation"}, {"file": "080-laravel-sanctum-guide.md", "link": "#13-configuration-setup", "text": "1.3. Configuration Setup", "status": "Anchor not found: #13-configuration-setup"}, {"file": "080-laravel-sanctum-guide.md", "link": "#21-token-generation", "text": "2.1. Token <PERSON>", "status": "Anchor not found: #21-token-generation"}, {"file": "080-laravel-sanctum-guide.md", "link": "#22-token-management", "text": "2.2. Token Management", "status": "Anchor not found: #22-token-management"}, {"file": "080-laravel-sanctum-guide.md", "link": "#23-token-abilities", "text": "2.3. Token Abilities", "status": "Anchor not found: #23-token-abilities"}, {"file": "080-laravel-sanctum-guide.md", "link": "#31-csrf-protection", "text": "3.1. CSRF Protection", "status": "Anchor not found: #31-csrf-protection"}, {"file": "080-laravel-sanctum-guide.md", "link": "#32-session-based-auth", "text": "3.2. Session-Based Auth", "status": "Anchor not found: #32-session-based-auth"}, {"file": "080-laravel-sanctum-guide.md", "link": "#33-frontend-integration", "text": "3.3. Frontend Integration", "status": "Anchor not found: #33-frontend-integration"}, {"file": "080-laravel-sanctum-guide.md", "link": "#41-mobile-token-flow", "text": "4.1. Mobile Token Flow", "status": "Anchor not found: #41-mobile-token-flow"}, {"file": "080-laravel-sanctum-guide.md", "link": "#42-device-management", "text": "4.2. Device Management", "status": "Anchor not found: #42-device-management"}, {"file": "080-laravel-sanctum-guide.md", "link": "#43-push-notifications", "text": "4.3. Push Notifications", "status": "Anchor not found: #43-push-notifications"}, {"file": "080-laravel-sanctum-guide.md", "link": "#security-best-practices", "text": "Security Best Practices", "status": "Anchor not found: #security-best-practices"}, {"file": "080-laravel-sanctum-guide.md", "link": "#rate-limiting", "text": "Rate Limiting", "status": "Anchor not found: #rate-limiting"}, {"file": "080-laravel-sanctum-guide.md", "link": "#testing-strategies", "text": "Testing Strategies", "status": "Anchor not found: #testing-strategies"}, {"file": "080-laravel-sanctum-guide.md", "link": "#advanced-features", "text": "Advanced Features", "status": "Anchor not found: #advanced-features"}, {"file": "080-laravel-sanctum-guide.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "101-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "101-laravel-query-builder-guide.md", "link": "#sorting--pagination", "text": "Sorting & Pagination", "status": "Anchor not found: #sorting--pagination"}, {"file": "101-laravel-query-builder-guide.md", "link": "#custom-filter-development", "text": "Custom Filter Development", "status": "Anchor not found: #custom-filter-development"}, {"file": "101-laravel-query-builder-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "101-laravel-query-builder-guide.md", "link": "110-spatie-comments-guide.md", "text": "Spatie Comments System Guide", "status": "File not found: 110-spatie-comments-guide.md"}, {"file": "101-laravel-query-builder-guide.md", "link": "090-laravel-workos-guide.md", "text": "Laravel WorkOS Guide", "status": "File not found: 090-laravel-workos-guide.md"}, {"file": "101-laravel-query-builder-guide.md", "link": "120-laravel-folio-guide.md", "text": "Laravel Folio Guide", "status": "File not found: 120-laravel-folio-guide.md"}, {"file": "151-spatie-activitylog-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "151-spatie-activitylog-guide.md", "link": "#security--compliance", "text": "Security & Compliance", "status": "Anchor not found: #security--compliance"}, {"file": "151-spatie-activitylog-guide.md", "link": "#real-time-activity-monitoring", "text": "Real-time Activity Monitoring", "status": "Anchor not found: #real-time-activity-monitoring"}, {"file": "151-spatie-activitylog-guide.md", "link": "#activity-analytics", "text": "Activity Analytics", "status": "Anchor not found: #activity-analytics"}, {"file": "151-spatie-activitylog-guide.md", "link": "#integration-patterns", "text": "Integration Patterns", "status": "Anchor not found: #integration-patterns"}, {"file": "151-spatie-activitylog-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "151-spatie-activitylog-guide.md", "link": "../testing/010-pest-testing-guide.md", "text": "Modern Testing with Pest Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../testing/010-pest-testing-guide.md"}, {"file": "151-spatie-activitylog-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "Path outside base directory: .ai/guides/chinook/packages/../development/010-debugbar-guide.md"}, {"file": "151-spatie-activitylog-guide.md", "link": "090-laravel-workos-guide.md", "text": "Laravel WorkOS Guide", "status": "File not found: 090-laravel-workos-guide.md"}, {"file": "091-laravel-workos-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "091-laravel-workos-guide.md", "link": "#monitoring--troubleshooting", "text": "Monitoring & Troubleshooting", "status": "Anchor not found: #monitoring--troubleshooting"}, {"file": "091-laravel-workos-guide.md", "link": "100-laravel-query-builder-guide.md", "text": "<PERSON><PERSON> Query Builder Guide", "status": "File not found: 100-laravel-query-builder-guide.md"}, {"file": "091-laravel-workos-guide.md", "link": "110-spatie-comments-guide.md", "text": "Spatie Comments System Guide", "status": "File not found: 110-spatie-comments-guide.md"}, {"file": "091-laravel-workos-guide.md", "link": "120-laravel-folio-guide.md", "text": "Laravel Folio Guide", "status": "File not found: 120-laravel-folio-guide.md"}, {"file": "020-laravel-pulse-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "020-laravel-pulse-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "020-laravel-pulse-guide.md", "link": "#12-database-configuration", "text": "1.2. Database Configuration", "status": "Anchor not found: #12-database-configuration"}, {"file": "020-laravel-pulse-guide.md", "link": "#13-environment-setup", "text": "1.3. Environment Setup", "status": "Anchor not found: #13-environment-setup"}, {"file": "020-laravel-pulse-guide.md", "link": "#21-basic-dashboard-setup", "text": "2.1. Basic Dashboard Setup", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"file": "020-laravel-pulse-guide.md", "link": "#22-custom-dashboard-layouts", "text": "2.2. Custom Dashboard Layouts", "status": "Anchor not found: #22-custom-dashboard-layouts"}, {"file": "020-laravel-pulse-guide.md", "link": "#23-authentication--authorization", "text": "2.3. Authentication & Authorization", "status": "Anchor not found: #23-authentication--authorization"}, {"file": "020-laravel-pulse-guide.md", "link": "#31-built-in-recorders", "text": "3.1. Built-in Recorders", "status": "Anchor not found: #31-built-in-recorders"}, {"file": "020-laravel-pulse-guide.md", "link": "#32-custom-metrics-collection", "text": "3.2. Custom Metrics Collection", "status": "Anchor not found: #32-custom-metrics-collection"}, {"file": "020-laravel-pulse-guide.md", "link": "#33-performance-monitoring", "text": "3.3. Performance Monitoring", "status": "Anchor not found: #33-performance-monitoring"}, {"file": "020-laravel-pulse-guide.md", "link": "#custom-metrics--cards", "text": "Custom Metrics & Cards", "status": "Anchor not found: #custom-metrics--cards"}, {"file": "020-laravel-pulse-guide.md", "link": "#41-creating-custom-recorders", "text": "4.1. Creating Custom Recorders", "status": "Anchor not found: #41-creating-custom-recorders"}, {"file": "020-laravel-pulse-guide.md", "link": "#42-building-custom-cards", "text": "4.2. Building Custom Cards", "status": "Anchor not found: #42-building-custom-cards"}, {"file": "020-laravel-pulse-guide.md", "link": "#43-business-metrics-integration", "text": "4.3. Business Metrics Integration", "status": "Anchor not found: #43-business-metrics-integration"}, {"file": "121-laravel-folio-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "121-laravel-folio-guide.md", "link": "#livewire-volt-integration", "text": "Livewire/Volt Integration", "status": "Anchor not found: #livewire-volt-integration"}, {"file": "121-laravel-folio-guide.md", "link": "#production-deployment", "text": "Production Deployment", "status": "Anchor not found: #production-deployment"}, {"file": "121-laravel-folio-guide.md", "link": "#real-world-examples", "text": "Real-World Examples", "status": "Anchor not found: #real-world-examples"}, {"file": "121-laravel-folio-guide.md", "link": "130-nn<PERSON><PERSON>-world-guide.md", "text": "NNJeim World Guide", "status": "File not found: 130-nnjeim-world-guide.md"}, {"file": "121-laravel-folio-guide.md", "link": "140-laravel-optimize-database-guide.md", "text": "Laravel Database Optimization Guide", "status": "File not found: 140-laravel-optimize-database-guide.md"}, {"file": "121-laravel-folio-guide.md", "link": "110-spatie-comments-guide.md", "text": "Spatie Comments Guide", "status": "File not found: 110-spatie-comments-guide.md"}, {"file": "140-spatie-laravel-query-builder-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "060-laravel-data-guide.md", "link": "#installation--setup", "text": "Installation & Setup", "status": "Anchor not found: #installation--setup"}, {"file": "060-laravel-data-guide.md", "link": "#11-package-installation", "text": "1.1. Package Installation", "status": "Anchor not found: #11-package-installation"}, {"file": "060-laravel-data-guide.md", "link": "#12-configuration-publishing", "text": "1.2. Configuration Publishing", "status": "Anchor not found: #12-configuration-publishing"}, {"file": "060-laravel-data-guide.md", "link": "#13-basic-setup", "text": "1.3. Basic Setup", "status": "Anchor not found: #13-basic-setup"}, {"file": "060-laravel-data-guide.md", "link": "#21-basic-dto-creation", "text": "2.1. Basic DTO Creation", "status": "Anchor not found: #21-basic-dto-creation"}, {"file": "060-laravel-data-guide.md", "link": "#22-advanced-dto-features", "text": "2.2. Advanced DTO Features", "status": "Anchor not found: #22-advanced-dto-features"}, {"file": "060-laravel-data-guide.md", "link": "#23-nested-dtos", "text": "2.3. Nested DTOs", "status": "Anchor not found: #23-nested-dtos"}, {"file": "060-laravel-data-guide.md", "link": "#validation--transformation", "text": "Validation & Transformation", "status": "Anchor not found: #validation--transformation"}, {"file": "060-laravel-data-guide.md", "link": "#31-built-in-validation", "text": "3.1. Built-in Validation", "status": "Anchor not found: #31-built-in-validation"}, {"file": "060-laravel-data-guide.md", "link": "#32-custom-validation-rules", "text": "3.2. Custom Validation Rules", "status": "Anchor not found: #32-custom-validation-rules"}, {"file": "060-laravel-data-guide.md", "link": "#33-data-transformation", "text": "3.3. Data Transformation", "status": "Anchor not found: #33-data-transformation"}, {"file": "060-laravel-data-guide.md", "link": "#41-api-resource-integration", "text": "4.1. API Resource Integration", "status": "Anchor not found: #41-api-resource-integration"}, {"file": "060-laravel-data-guide.md", "link": "#42-request-handling", "text": "4.2. Request Handling", "status": "Anchor not found: #42-request-handling"}, {"file": "060-laravel-data-guide.md", "link": "#43-response-formatting", "text": "4.3. Response Formatting", "status": "Anchor not found: #43-response-formatting"}, {"file": "060-laravel-data-guide.md", "link": "#collections--arrays", "text": "Collections & Arrays", "status": "Anchor not found: #collections--arrays"}, {"file": "111-spatie-comments-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "111-spatie-comments-guide.md", "link": "#rbac-integration", "text": "RBAC Integration", "status": "Anchor not found: #rbac-integration"}, {"file": "111-spatie-comments-guide.md", "link": "#real-time-updates", "text": "Real-time Updates", "status": "Anchor not found: #real-time-updates"}, {"file": "111-spatie-comments-guide.md", "link": "#spam-prevention", "text": "Spam Prevention", "status": "Anchor not found: #spam-prevention"}, {"file": "111-spatie-comments-guide.md", "link": "090-laravel-workos-guide.md", "text": "Laravel WorkOS Guide", "status": "File not found: 090-laravel-workos-guide.md"}, {"file": "111-spatie-comments-guide.md", "link": "100-laravel-query-builder-guide.md", "text": "Laravel Query Builder Guide", "status": "File not found: 100-laravel-query-builder-guide.md"}, {"file": "111-spatie-comments-guide.md", "link": "120-laravel-folio-guide.md", "text": "Laravel Folio Guide", "status": "File not found: 120-laravel-folio-guide.md"}, {"file": "testing/000-testing-index.md", "link": "../development/000-development-index.md", "text": "Development Tools Index", "status": "File not found: development/000-development-index.md"}, {"file": "testing/000-testing-index.md", "link": "../../000-chinook-index.md", "text": "Main Chinook Index", "status": "Path outside base directory: .ai/guides/chinook/packages/testing/../../000-chinook-index.md"}, {"file": "testing/010-pest-testing-guide.md", "link": "#installation--configuration", "text": "Installation & Configuration", "status": "Anchor not found: #installation--configuration"}, {"file": "testing/010-pest-testing-guide.md", "link": "#advanced-testing-patterns", "text": "Advanced Testing Patterns", "status": "Anchor not found: #advanced-testing-patterns"}, {"file": "testing/010-pest-testing-guide.md", "link": "#type-coverage", "text": "Type Coverage", "status": "Anchor not found: #type-coverage"}, {"file": "testing/010-pest-testing-guide.md", "link": "#api-testing", "text": "API Testing", "status": "Anchor not found: #api-testing"}, {"file": "testing/010-pest-testing-guide.md", "link": "#livewire-testing", "text": "Livewire Testing", "status": "Anchor not found: #livewire-testing"}, {"file": "testing/010-pest-testing-guide.md", "link": "../development/010-debugbar-guide.md", "text": "Development Debugging Tools Guide", "status": "File not found: development/010-debugbar-guide.md"}, {"file": "testing/010-pest-testing-guide.md", "link": "../development/020-pint-code-quality-guide.md", "text": "Code Quality and Formatting Guide", "status": "File not found: development/020-pint-code-quality-guide.md"}, {"file": "testing/010-pest-testing-guide.md", "link": "../packages/150-spatie-activitylog-guide.md", "text": "Enhanced Spatie ActivityLog Guide", "status": "File not found: packages/150-spatie-activitylog-guide.md"}], "resolved_issues": [], "status": "FAIL", "execution_time": 0.11306428909301758}, "detailed_results": {"030-laravel-telescope-guide.md": {"file": "030-laravel-telescope-guide.md", "total_links": 25, "internal_links": 2, "anchor_links": 23, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Configuration Publishing", "url": "#12-configuration-publishing", "type": "anchor", "status": "Anchor not found: #12-configuration-publishing"}, {"text": "1.3. Environment Configuration", "url": "#13-environment-configuration", "type": "anchor", "status": "Anchor not found: #13-environment-configuration"}, {"text": "Authorization & Security", "url": "#authorization--security", "type": "anchor", "status": "Anchor not found: #authorization--security"}, {"text": "2.1. Gate-Based Authorization", "url": "#21-gate-based-authorization", "type": "anchor", "status": "Anchor not found: #21-gate-based-authorization"}, {"text": "2.2. Environment-Specific Access", "url": "#22-environment-specific-access", "type": "anchor", "status": "Anchor not found: #22-environment-specific-access"}, {"text": "2.3. IP Whitelisting", "url": "#23-ip-whitelisting", "type": "anchor", "status": "Anchor not found: #23-ip-whitelisting"}, {"text": "3.1. Watcher Configuration", "url": "#31-watcher-configuration", "type": "anchor", "status": "Anchor not found: #31-watcher-configuration"}, {"text": "3.2. Fi<PERSON><PERSON> & Sam<PERSON>", "url": "#32-filtering--sampling", "type": "anchor", "status": "Anchor not found: #32-filtering--sampling"}, {"text": "3.3. Performance Impact Mitigation", "url": "#33-performance-impact-mitigation", "type": "anchor", "status": "Anchor not found: #33-performance-impact-mitigation"}, {"text": "Data Pruning & Storage Management", "url": "#data-pruning--storage-management", "type": "anchor", "status": "Anchor not found: #data-pruning--storage-management"}, {"text": "4.1. Automated Pruning", "url": "#41-automated-pruning", "type": "anchor", "status": "Anchor not found: #41-automated-pruning"}, {"text": "4.2. Storage Optimization", "url": "#42-storage-optimization", "type": "anchor", "status": "Anchor not found: #42-storage-optimization"}, {"text": "4.3. Custom Retention Policies", "url": "#43-custom-retention-policies", "type": "anchor", "status": "Anchor not found: #43-custom-retention-policies"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Data Collection Configuration", "url": "#data-collection-configuration", "type": "anchor", "status": "Anchor found: Data Collection Configuration"}, {"text": "Debugging Workflows", "url": "#debugging-workflows", "type": "anchor", "status": "Anchor found: Debugging Workflows"}, {"text": "Production Considerations", "url": "#production-considerations", "type": "anchor", "status": "Anchor found: Production Considerations"}, {"text": "Integration Strategies", "url": "#integration-strategies", "type": "anchor", "status": "Anchor found: Integration Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON>", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: 020-laravel-pulse-guide.md"}, {"text": "<PERSON><PERSON>ane with FrankenPHP Guide", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: 040-laravel-octane-frankenphp-guide.md"}]}, "130-spatie-laravel-settings-guide.md": {"file": "130-spatie-laravel-settings-guide.md", "total_links": 11, "internal_links": 3, "anchor_links": 8, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "NNJeim World Guide", "url": "120-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File not found: 120-nnjeim-world-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Usage", "url": "#basic-usage", "type": "anchor", "status": "Anchor found: Basic Usage"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Integration with Chinook", "url": "#integration-with-chinook", "type": "anchor", "status": "Anchor found: Integration with Chinook"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON> Query Builder Guide", "url": "140-spatie-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 140-spatie-laravel-query-builder-guide.md"}, {"text": "Package Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "100-spatie-media-library-guide.md": {"file": "100-spatie-media-library-guide.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "File Conversions & Processing", "url": "#file-conversions--processing", "type": "anchor", "status": "Anchor not found: #file-conversions--processing"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Media Implementation", "url": "#basic-media-implementation", "type": "anchor", "status": "Anchor found: Basic Media Implementation"}, {"text": "Advanced Media Patterns", "url": "#advanced-media-patterns", "type": "anchor", "status": "Anchor found: Advanced Media Patterns"}, {"text": "Chinook Integration", "url": "#chinook-integration", "type": "anchor", "status": "Anchor found: Chinook Integration"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "CDN Integration", "url": "#cdn-integration", "type": "anchor", "status": "Anchor found: CDN Integration"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Previous: <PERSON><PERSON> Guide", "url": "090-spatie-tags-guide.md", "type": "internal", "status": "File exists: 090-spatie-tags-guide.md"}, {"text": "Spatie Permission Guide", "url": "110-spatie-permission-guide.md", "type": "internal", "status": "File exists: 110-spatie-permission-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "050-laravel-horizon-guide.md": {"file": "050-laravel-horizon-guide.md", "total_links": 25, "internal_links": 2, "anchor_links": 23, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Configuration Publishing", "url": "#12-configuration-publishing", "type": "anchor", "status": "Anchor not found: #12-configuration-publishing"}, {"text": "1.3. Environment Setup", "url": "#13-environment-setup", "type": "anchor", "status": "Anchor not found: #13-environment-setup"}, {"text": "2.1. Basic Dashboard Setup", "url": "#21-basic-dashboard-setup", "type": "anchor", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"text": "2.2. Authentication & Authorization", "url": "#22-authentication--authorization", "type": "anchor", "status": "Anchor not found: #22-authentication--authorization"}, {"text": "2.3. Custom Dashboard Views", "url": "#23-custom-dashboard-views", "type": "anchor", "status": "Anchor not found: #23-custom-dashboard-views"}, {"text": "3.1. <PERSON><PERSON> Worker Settings", "url": "#31-queue-worker-settings", "type": "anchor", "status": "Anchor not found: #31-queue-worker-settings"}, {"text": "3.2. Supervisor Configuration", "url": "#32-supervisor-configuration", "type": "anchor", "status": "Anchor not found: #32-supervisor-configuration"}, {"text": "3.3. Auto-Scaling Setup", "url": "#33-auto-scaling-setup", "type": "anchor", "status": "Anchor not found: #33-auto-scaling-setup"}, {"text": "4.1. Horizon Watcher Integration", "url": "#41-horizon-watcher-integration", "type": "anchor", "status": "Anchor not found: #41-horizon-watcher-integration"}, {"text": "4.2. Custom Metrics Collection", "url": "#42-custom-metrics-collection", "type": "anchor", "status": "Anchor not found: #42-custom-metrics-collection"}, {"text": "4.3. <PERSON><PERSON>gu<PERSON>", "url": "#43-alert-configuration", "type": "anchor", "status": "Anchor not found: #43-alert-configuration"}, {"text": "Performance Tuning", "url": "#performance-tuning", "type": "anchor", "status": "Anchor not found: #performance-tuning"}, {"text": "Integration Strategies", "url": "#integration-strategies", "type": "anchor", "status": "Anchor not found: #integration-strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor not found: #best-practices"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor not found: #troubleshooting"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Dashboard Configuration", "url": "#dashboard-configuration", "type": "anchor", "status": "Anchor found: Dashboard Configuration"}, {"text": "Worker Configuration", "url": "#worker-configuration", "type": "anchor", "status": "Anchor found: Worker Configuration"}, {"text": "Enhanced Monitoring", "url": "#enhanced-monitoring", "type": "anchor", "status": "Anchor found: Enhanced Monitoring"}, {"text": "Deployment Procedures", "url": "#deployment-procedures", "type": "anchor", "status": "Anchor found: Deployment Procedures"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON>ane with FrankenPHP Guide", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: 040-laravel-octane-frankenphp-guide.md"}, {"text": "Laravel Data Guide", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: 060-laravel-data-guide.md"}]}, "000-packages-index.md": {"file": "000-packages-index.md", "total_links": 62, "internal_links": 32, "anchor_links": 30, "external_links": 0, "broken_links": [{"text": "Backup & Monitoring", "url": "#backup--monitoring", "type": "anchor", "status": "Anchor not found: #backup--monitoring"}, {"text": "Performance & Optimization", "url": "#performance--optimization", "type": "anchor", "status": "Anchor not found: #performance--optimization"}, {"text": "1. <PERSON><PERSON>", "url": "#1-<PERSON><PERSON><PERSON>-backup", "type": "anchor", "status": "Anchor not found: #1-la<PERSON>l-backup"}, {"text": "2. <PERSON><PERSON>", "url": "#2-laravel-pulse", "type": "anchor", "status": "Anchor not found: #2-laravel-pulse"}, {"text": "3. <PERSON><PERSON> Teles<PERSON>", "url": "#3-laravel-telescope", "type": "anchor", "status": "Anchor not found: #3-la<PERSON>l-telescope"}, {"text": "4. <PERSON><PERSON> with FrankenPHP", "url": "#4-laravel-octane-with-frankenphp", "type": "anchor", "status": "Anchor not found: #4-laravel-octane-with-frankenphp"}, {"text": "5. <PERSON><PERSON>", "url": "#5-laravel-horizon", "type": "anchor", "status": "Anchor not found: #5-laravel-horizon"}, {"text": "6. <PERSON><PERSON>", "url": "#6-laravel-data", "type": "anchor", "status": "Anchor not found: #6-laravel-data"}, {"text": "7. <PERSON><PERSON>", "url": "#7-laravel-fractal", "type": "anchor", "status": "Anchor not found: #7-laravel-fractal"}, {"text": "8. <PERSON><PERSON>", "url": "#8-laravel-sanctum", "type": "anchor", "status": "Anchor not found: #8-laravel-sanctum"}, {"text": "9. <PERSON><PERSON>", "url": "#9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "type": "anchor", "status": "Anchor not found: #9-<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"text": "10. <PERSON><PERSON> Query Builder", "url": "#10-laravel-query-builder", "type": "anchor", "status": "Anchor not found: #10-laravel-query-builder"}, {"text": "11. <PERSON><PERSON>", "url": "#11-spatie-comments", "type": "anchor", "status": "Anchor not found: #11-spatie-comments"}, {"text": "12. <PERSON><PERSON>", "url": "#12-laravel-folio", "type": "anchor", "status": "Anchor not found: #12-laravel-folio"}, {"text": "13. NNJeim World", "url": "#13-nn<PERSON><PERSON>-world", "type": "anchor", "status": "Anchor not found: #13-nn<PERSON><PERSON>-world"}, {"text": "14. Laravel Database Optimization", "url": "#14-laravel-database-optimization", "type": "anchor", "status": "Anchor not found: #14-laravel-database-optimization"}, {"text": "15. Enhanced Spatie ActivityLog", "url": "#15-enhanced-spatie-activitylog", "type": "anchor", "status": "Anchor not found: #15-enhanced-spatie-activitylog"}, {"text": "Laravel Database Optimization Guide", "url": "140-laravel-database-optimization-guide.md", "type": "internal", "status": "File not found: 140-laravel-database-optimization-guide.md"}, {"text": "Enhanced Spatie ActivityLog Guide", "url": "150-enhanced-spatie-activitylog-guide.md", "type": "internal", "status": "File not found: 150-enhanced-spatie-activitylog-guide.md"}, {"text": "Chinook Hierarchy Comparison Guide", "url": "../070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../070-chinook-hierarchy-comparison-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Package Categories", "url": "#package-categories", "type": "anchor", "status": "Anchor found: Package Categories"}, {"text": "API Development", "url": "#api-development", "type": "anchor", "status": "Anchor found: API Development"}, {"text": "Queue Management", "url": "#queue-management", "type": "anchor", "status": "Anchor found: Queue Management"}, {"text": "Data Transformation", "url": "#data-transformation", "type": "anchor", "status": "Anchor found: Data Transformation"}, {"text": "Enterprise Authentication", "url": "#enterprise-authentication", "type": "anchor", "status": "Anchor found: Enterprise Authentication"}, {"text": "User Engagement", "url": "#user-engagement", "type": "anchor", "status": "Anchor found: User Engagement"}, {"text": "Geographic Data", "url": "#geographic-data", "type": "anchor", "status": "Anchor found: Geographic Data"}, {"text": "Activity Logging", "url": "#activity-logging", "type": "anchor", "status": "An<PERSON> found: Activity Logging"}, {"text": "Implementation Guides", "url": "#implementation-guides", "type": "anchor", "status": "Anchor found: Implementation Guides"}, {"text": "Integration Patterns", "url": "#integration-patterns", "type": "anchor", "status": "Anchor found: Integration Patterns"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "010-la<PERSON>l-backup-guide.md", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: 010-laravel-backup-guide.md"}, {"text": "020-laravel-pulse-guide.md", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: 020-laravel-pulse-guide.md"}, {"text": "030-laravel-telescope-guide.md", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: 030-laravel-telescope-guide.md"}, {"text": "040-laravel-octane-frankenphp-guide.md", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: 040-laravel-octane-frankenphp-guide.md"}, {"text": "050-laravel-horizon-guide.md", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: 050-laravel-horizon-guide.md"}, {"text": "060-laravel-data-guide.md", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: 060-laravel-data-guide.md"}, {"text": "070-laravel-fractal-guide.md", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: 070-laravel-fractal-guide.md"}, {"text": "080-laravel-sanctum-guide.md", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: 080-laravel-sanctum-guide.md"}, {"text": "091-laravel-workos-guide.md", "url": "091-laravel-workos-guide.md", "type": "internal", "status": "File exists: 091-laravel-workos-guide.md"}, {"text": "101-laravel-query-builder-guide.md", "url": "101-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 101-laravel-query-builder-guide.md"}, {"text": "111-spatie-comments-guide.md", "url": "111-spatie-comments-guide.md", "type": "internal", "status": "File exists: 111-spatie-comments-guide.md"}, {"text": "121-laravel-folio-guide.md", "url": "121-laravel-folio-guide.md", "type": "internal", "status": "File exists: 121-laravel-folio-guide.md"}, {"text": "131-nn<PERSON><PERSON>-world-guide.md", "url": "131-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File exists: 131-nnjeim-world-guide.md"}, {"text": "141-laravel-optimize-database-guide.md", "url": "141-laravel-optimize-database-guide.md", "type": "internal", "status": "File exists: 141-laravel-optimize-database-guide.md"}, {"text": "151-spatie-activitylog-guide.md", "url": "151-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: 151-spatie-activitylog-guide.md"}, {"text": "<PERSON><PERSON>up Guide", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: 010-laravel-backup-guide.md"}, {"text": "<PERSON><PERSON>", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: 020-laravel-pulse-guide.md"}, {"text": "Laravel Telescope Guide", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: 030-laravel-telescope-guide.md"}, {"text": "<PERSON><PERSON>ane with FrankenPHP Guide", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: 040-laravel-octane-frankenphp-guide.md"}, {"text": "Laravel Horizon Guide", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: 050-laravel-horizon-guide.md"}, {"text": "Laravel Data Guide", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: 060-laravel-data-guide.md"}, {"text": "<PERSON><PERSON>", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: 070-laravel-fractal-guide.md"}, {"text": "Laravel Sanctum Guide", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: 080-laravel-sanctum-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "091-laravel-workos-guide.md", "type": "internal", "status": "File exists: 091-laravel-workos-guide.md"}, {"text": "Laravel Query Builder Guide", "url": "101-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 101-laravel-query-builder-guide.md"}, {"text": "Spatie Comments Guide", "url": "111-spatie-comments-guide.md", "type": "internal", "status": "File exists: 111-spatie-comments-guide.md"}, {"text": "Laravel Folio Guide", "url": "121-laravel-folio-guide.md", "type": "internal", "status": "File exists: 121-laravel-folio-guide.md"}, {"text": "NNJeim World Guide", "url": "131-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File exists: 131-nnjeim-world-guide.md"}, {"text": "<PERSON><PERSON>up Guide", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: 010-laravel-backup-guide.md"}]}, "141-laravel-optimize-database-guide.md": {"file": "141-laravel-optimize-database-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Query Performance Monitoring", "url": "#query-performance-monitoring", "type": "anchor", "status": "Anchor not found: #query-performance-monitoring"}, {"text": "SQLite-Specific Optimizations", "url": "#sqlite-specific-optimizations", "type": "anchor", "status": "Anchor not found: #sqlite-specific-optimizations"}, {"text": "Performance Testing", "url": "#performance-testing", "type": "anchor", "status": "Anchor not found: #performance-testing"}, {"text": "Production Monitoring", "url": "#production-monitoring", "type": "anchor", "status": "Anchor not found: #production-monitoring"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor not found: #troubleshooting"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor not found: #best-practices"}, {"text": "Enhanced Spatie ActivityLog Guide", "url": "150-spatie-activitylog-guide.md", "type": "internal", "status": "File not found: 150-spatie-activitylog-guide.md"}, {"text": "Modern Testing with Pest Guide", "url": "../testing/010-pest-testing-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../testing/010-pest-testing-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File not found: 090-laravel-workos-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Database Optimization Strategies", "url": "#database-optimization-strategies", "type": "anchor", "status": "Anchor found: Database Optimization Strategies"}, {"text": "Index Optimization", "url": "#index-optimization", "type": "anchor", "status": "Anchor found: Index Optimization"}, {"text": "Laravel Pulse Integration", "url": "#laravel-pulse-integration", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>"}, {"text": "Automated Optimization Workflows", "url": "#automated-optimization-workflows", "type": "anchor", "status": "Anchor found: Automated Optimization Workflows"}]}, "010-laravel-backup-guide.md": {"file": "010-la<PERSON>l-backup-guide.md", "total_links": 30, "internal_links": 2, "anchor_links": 28, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Configuration Publishing", "url": "#12-configuration-publishing", "type": "anchor", "status": "Anchor not found: #12-configuration-publishing"}, {"text": "1.3. Environment Configuration", "url": "#13-environment-configuration", "type": "anchor", "status": "Anchor not found: #13-environment-configuration"}, {"text": "2.1. Local Storage Setup", "url": "#21-local-storage-setup", "type": "anchor", "status": "Anchor not found: #21-local-storage-setup"}, {"text": "2.2. AWS S3 Configuration", "url": "#22-aws-s3-configuration", "type": "anchor", "status": "Anchor not found: #22-aws-s3-configuration"}, {"text": "2.3. Google Cloud Storage", "url": "#23-google-cloud-storage", "type": "anchor", "status": "Anchor not found: #23-google-cloud-storage"}, {"text": "2.4. Multi-Destination Strategy", "url": "#24-multi-destination-strategy", "type": "anchor", "status": "Anchor not found: #24-multi-destination-strategy"}, {"text": "3.1. Basic Backup Settings", "url": "#31-basic-backup-settings", "type": "anchor", "status": "Anchor not found: #31-basic-backup-settings"}, {"text": "3.2. Database Backup Options", "url": "#32-database-backup-options", "type": "anchor", "status": "Anchor not found: #32-database-backup-options"}, {"text": "3.3. File Backup Configuration", "url": "#33-file-backup-configuration", "type": "anchor", "status": "Anchor not found: #33-file-backup-configuration"}, {"text": "3.4. <PERSON><PERSON><PERSON><PERSON>", "url": "#34-exclusion-patterns", "type": "anchor", "status": "Anchor not found: #34-exclusion-patterns"}, {"text": "4.1. <PERSON><PERSON> Scheduler", "url": "#41-laravel-task-scheduler", "type": "anchor", "status": "Anchor not found: #41-laravel-task-scheduler"}, {"text": "4.2. <PERSON>ron Configuration", "url": "#42-cron-configuration", "type": "anchor", "status": "Anchor not found: #42-cron-configuration"}, {"text": "4.3. <PERSON><PERSON> Frequency Strategies", "url": "#43-backup-frequency-strategies", "type": "anchor", "status": "Anchor not found: #43-backup-frequency-strategies"}, {"text": "5.1. Email Notifications", "url": "#51-email-notifications", "type": "anchor", "status": "Anchor not found: #51-email-notifications"}, {"text": "5.2. <PERSON><PERSON><PERSON>", "url": "#52-slack-integration", "type": "anchor", "status": "Anchor not found: #52-slack-integration"}, {"text": "5.3. Custom Notification Channels", "url": "#53-custom-notification-channels", "type": "anchor", "status": "Anchor not found: #53-custom-notification-channels"}, {"text": "Monitoring & Health Checks", "url": "#monitoring--health-checks", "type": "anchor", "status": "Anchor not found: #monitoring--health-checks"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Storage Configuration", "url": "#storage-configuration", "type": "anchor", "status": "Anchor found: Storage Configuration"}, {"text": "Backup Configuration", "url": "#backup-configuration", "type": "anchor", "status": "Anchor found: Backup Configuration"}, {"text": "Automated Scheduling", "url": "#automated-scheduling", "type": "anchor", "status": "Anchor found: Automated Scheduling"}, {"text": "Notification Setup", "url": "#notification-setup", "type": "anchor", "status": "Anchor found: Notification Setup"}, {"text": "Restoration Procedures", "url": "#restoration-procedures", "type": "anchor", "status": "Anchor found: Restoration Procedures"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Laravel Package Implementation Guides", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}, {"text": "<PERSON><PERSON>", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: 020-laravel-pulse-guide.md"}]}, "040-laravel-octane-frankenphp-guide.md": {"file": "040-laravel-octane-frankenphp-guide.md", "total_links": 24, "internal_links": 2, "anchor_links": 22, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. FrankenPHP Server Setup", "url": "#12-frankenphp-server-setup", "type": "anchor", "status": "Anchor not found: #12-frankenphp-server-setup"}, {"text": "1.3. Environment Configuration", "url": "#13-environment-configuration", "type": "anchor", "status": "Anchor not found: #13-environment-configuration"}, {"text": "2.1. Basic Server Settings", "url": "#21-basic-server-settings", "type": "anchor", "status": "Anchor not found: #21-basic-server-settings"}, {"text": "2.2. Performance Optimization", "url": "#22-performance-optimization", "type": "anchor", "status": "Anchor not found: #22-performance-optimization"}, {"text": "2.3. SSL/TLS Configuration", "url": "#23-ssltls-configuration", "type": "anchor", "status": "Anchor not found: #23-ssltls-configuration"}, {"text": "3.1. Memory Leak Prevention", "url": "#31-memory-leak-prevention", "type": "anchor", "status": "Anchor not found: #31-memory-leak-prevention"}, {"text": "3.2. Resource Optimization", "url": "#32-resource-optimization", "type": "anchor", "status": "Anchor not found: #32-resource-optimization"}, {"text": "3.3. Garbage Collection Tuning", "url": "#33-garbage-collection-tuning", "type": "anchor", "status": "Anchor not found: #33-garbage-collection-tuning"}, {"text": "4.1. <PERSON><PERSON> Configuration", "url": "#41-docker-configuration", "type": "anchor", "status": "Anchor not found: #41-docker-configuration"}, {"text": "4.2. <PERSON><PERSON>", "url": "#42-load-balancing", "type": "anchor", "status": "Anchor not found: #42-load-balancing"}, {"text": "4.3. Scaling Strategies", "url": "#43-scaling-strategies", "type": "anchor", "status": "Anchor not found: #43-scaling-strategies"}, {"text": "Monitoring & Troubleshooting", "url": "#monitoring--troubleshooting", "type": "anchor", "status": "Anchor not found: #monitoring--troubleshooting"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Server Configuration", "url": "#server-configuration", "type": "anchor", "status": "Anchor found: Server Configuration"}, {"text": "Memory Management", "url": "#memory-management", "type": "anchor", "status": "Anchor found: Memory management"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Integration Strategies", "url": "#integration-strategies", "type": "anchor", "status": "Anchor found: Integration Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Performance Benchmarks", "url": "#performance-benchmarks", "type": "anchor", "status": "Anchor found: Performance Benchmarks"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Laravel Telescope Guide", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: 030-laravel-telescope-guide.md"}, {"text": "Laravel Horizon Guide", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: 050-laravel-horizon-guide.md"}]}, "120-spatie-activitylog-guide.md": {"file": "120-spatie-activitylog-guide.md", "total_links": 17, "internal_links": 5, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Security & Compliance", "url": "#security--compliance", "type": "anchor", "status": "Anchor not found: #security--compliance"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Activity Logging", "url": "#basic-activity-logging", "type": "anchor", "status": "An<PERSON> found: Basic Activity Logging"}, {"text": "Advanced Logging Patterns", "url": "#advanced-logging-patterns", "type": "anchor", "status": "Anchor found: Advanced Logging Patterns"}, {"text": "Chinook Integration", "url": "#chinook-integration", "type": "anchor", "status": "Anchor found: Chinook Integration"}, {"text": "Custom Activity Models", "url": "#custom-activity-models", "type": "anchor", "status": "Anchor found: Custom Activity Models"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Real-time Activity Monitoring", "url": "#real-time-activity-monitoring", "type": "anchor", "status": "An<PERSON> found: Real-time Activity Monitoring"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Previous: <PERSON><PERSON> Permission Guide", "url": "110-spatie-permission-guide.md", "type": "internal", "status": "File exists: 110-spatie-permission-guide.md"}, {"text": "<PERSON><PERSON> Settings Guide", "url": "130-spatie-laravel-settings-guide.md", "type": "internal", "status": "File exists: 130-spatie-laravel-settings-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "070-laravel-fractal-guide.md": {"file": "070-laravel-fractal-guide.md", "total_links": 25, "internal_links": 2, "anchor_links": 23, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Configuration Publishing", "url": "#12-configuration-publishing", "type": "anchor", "status": "Anchor not found: #12-configuration-publishing"}, {"text": "1.3. Basic Setup", "url": "#13-basic-setup", "type": "anchor", "status": "Anchor not found: #13-basic-setup"}, {"text": "2.1. Basic Transformers", "url": "#21-basic-transformers", "type": "anchor", "status": "Anchor not found: #21-basic-transformers"}, {"text": "2.2. Advanced Transformers", "url": "#22-advanced-transformers", "type": "anchor", "status": "Anchor not found: #22-advanced-transformers"}, {"text": "2.3. Nested Transformers", "url": "#23-nested-transformers", "type": "anchor", "status": "Anchor not found: #23-nested-transformers"}, {"text": "3.1. Item Resources", "url": "#31-item-resources", "type": "anchor", "status": "Anchor not found: #31-item-resources"}, {"text": "3.2. Collection Resources", "url": "#32-collection-resources", "type": "anchor", "status": "Anchor not found: #32-collection-resources"}, {"text": "3.3. Relationship Handling", "url": "#33-relationship-handling", "type": "anchor", "status": "Anchor not found: #33-relationship-handling"}, {"text": "Pagination & Filtering", "url": "#pagination--filtering", "type": "anchor", "status": "Anchor not found: #pagination--filtering"}, {"text": "4.1. Pa<PERSON>ation Setup", "url": "#41-pagination-setup", "type": "anchor", "status": "Anchor not found: #41-pagination-setup"}, {"text": "4.2. Advanced Filtering", "url": "#42-advanced-filtering", "type": "anchor", "status": "Anchor not found: #42-advanced-filtering"}, {"text": "4.3. Sorting & Ordering", "url": "#43-sorting--ordering", "type": "anchor", "status": "Anchor not found: #43-sorting--ordering"}, {"text": "API Response Formatting", "url": "#api-response-formatting", "type": "anchor", "status": "Anchor not found: #api-response-formatting"}, {"text": "Caching Integration", "url": "#caching-integration", "type": "anchor", "status": "Anchor not found: #caching-integration"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor not found: #performance-optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor not found: #testing-strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor not found: #best-practices"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Transformer Creation", "url": "#transformer-creation", "type": "anchor", "status": "Anchor found: Transformer Creation"}, {"text": "Resource Management", "url": "#resource-management", "type": "anchor", "status": "Anchor found: Resource Management"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Laravel Data Guide", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: 060-laravel-data-guide.md"}, {"text": "Laravel Sanctum Guide", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: 080-laravel-sanctum-guide.md"}]}, "131-nnjeim-world-guide.md": {"file": "131-nn<PERSON><PERSON>-world-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Country, State & City Management", "url": "#country-state--city-management", "type": "anchor", "status": "Anchor not found: #country-state--city-management"}, {"text": "User Profile Integration", "url": "#user-profile-integration", "type": "anchor", "status": "Anchor not found: #user-profile-integration"}, {"text": "Business Logic Integration", "url": "#business-logic-integration", "type": "anchor", "status": "Anchor not found: #business-logic-integration"}, {"text": "Laravel Database Optimization Guide", "url": "140-laravel-optimize-database-guide.md", "type": "internal", "status": "File not found: 140-laravel-optimize-database-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File not found: 090-laravel-workos-guide.md"}, {"text": "Laravel Query Builder Guide", "url": "100-laravel-query-builder-guide.md", "type": "internal", "status": "File not found: 100-laravel-query-builder-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Geographic Data Installation", "url": "#geographic-data-installation", "type": "anchor", "status": "Anchor found: Geographic Data Installation"}, {"text": "API Integration Patterns", "url": "#api-integration-patterns", "type": "anchor", "status": "Anchor found: API Integration Patterns"}, {"text": "Frontend Components", "url": "#frontend-components", "type": "anchor", "status": "Anchor found: Frontend Components"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}]}, "090-spatie-tags-guide.md": {"file": "090-spatie-tags-guide.md", "total_links": 15, "internal_links": 5, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Tag Implementation", "url": "#basic-tag-implementation", "type": "anchor", "status": "Anchor found: Basic Tag Implementation"}, {"text": "Advanced Tagging Patterns", "url": "#advanced-tagging-patterns", "type": "anchor", "status": "Anchor found: Advanced Tagging Patterns"}, {"text": "Chinook Integration", "url": "#chinook-integration", "type": "anchor", "status": "Anchor found: Chinook Integration"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "API Integration", "url": "#api-integration", "type": "anchor", "status": "Anchor found: API Integration"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Previous: <PERSON><PERSON>", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: 080-laravel-sanctum-guide.md"}, {"text": "Spatie Media Library Guide", "url": "100-spatie-media-library-guide.md", "type": "internal", "status": "File exists: 100-spatie-media-library-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "150-spatie-laravel-translatable-guide.md": {"file": "150-spatie-laravel-translatable-guide.md", "total_links": 11, "internal_links": 3, "anchor_links": 8, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Package Integration Patterns", "url": "../integration-patterns.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../integration-patterns.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Usage", "url": "#basic-usage", "type": "anchor", "status": "Anchor found: Basic Usage"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Integration with Chinook", "url": "#integration-with-chinook", "type": "anchor", "status": "Anchor found: Integration with Chinook"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON> Query Builder Guide", "url": "140-spatie-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 140-spatie-laravel-query-builder-guide.md"}, {"text": "Package Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "110-spatie-permission-guide.md": {"file": "110-spatie-permission-guide.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../010-chinook-models-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic RBAC Implementation", "url": "#basic-rbac-implementation", "type": "anchor", "status": "Anchor found: Basic RBAC Implementation"}, {"text": "Advanced Permission Patterns", "url": "#advanced-permission-patterns", "type": "anchor", "status": "Anchor found: Advanced Permission Patterns"}, {"text": "Chinook Integration", "url": "#chinook-integration", "type": "anchor", "status": "Anchor found: Chinook Integration"}, {"text": "Hierarchical Roles", "url": "#hierarchical-roles", "type": "anchor", "status": "Anchor found: Hierarchical Roles"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "API Integration", "url": "#api-integration", "type": "anchor", "status": "Anchor found: API Integration"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Previous: Spatie Media Library Guide", "url": "100-spatie-media-library-guide.md", "type": "internal", "status": "File exists: 100-spatie-media-library-guide.md"}, {"text": "Spatie ActivityLog Guide", "url": "120-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: 120-spatie-activitylog-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "080-laravel-sanctum-guide.md": {"file": "080-laravel-sanctum-guide.md", "total_links": 25, "internal_links": 2, "anchor_links": 23, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. API Installation", "url": "#12-api-installation", "type": "anchor", "status": "Anchor not found: #12-api-installation"}, {"text": "1.3. Configuration Setup", "url": "#13-configuration-setup", "type": "anchor", "status": "Anchor not found: #13-configuration-setup"}, {"text": "2.1. Token <PERSON>", "url": "#21-token-generation", "type": "anchor", "status": "Anchor not found: #21-token-generation"}, {"text": "2.2. Token Management", "url": "#22-token-management", "type": "anchor", "status": "Anchor not found: #22-token-management"}, {"text": "2.3. Token Abilities", "url": "#23-token-abilities", "type": "anchor", "status": "Anchor not found: #23-token-abilities"}, {"text": "3.1. CSRF Protection", "url": "#31-csrf-protection", "type": "anchor", "status": "Anchor not found: #31-csrf-protection"}, {"text": "3.2. Session-Based Auth", "url": "#32-session-based-auth", "type": "anchor", "status": "Anchor not found: #32-session-based-auth"}, {"text": "3.3. Frontend Integration", "url": "#33-frontend-integration", "type": "anchor", "status": "Anchor not found: #33-frontend-integration"}, {"text": "4.1. Mobile Token Flow", "url": "#41-mobile-token-flow", "type": "anchor", "status": "Anchor not found: #41-mobile-token-flow"}, {"text": "4.2. Device Management", "url": "#42-device-management", "type": "anchor", "status": "Anchor not found: #42-device-management"}, {"text": "4.3. Push Notifications", "url": "#43-push-notifications", "type": "anchor", "status": "Anchor not found: #43-push-notifications"}, {"text": "Security Best Practices", "url": "#security-best-practices", "type": "anchor", "status": "Anchor not found: #security-best-practices"}, {"text": "Rate Limiting", "url": "#rate-limiting", "type": "anchor", "status": "Anchor not found: #rate-limiting"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor not found: #testing-strategies"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor not found: #advanced-features"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor not found: #troubleshooting"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "API Token Authentication", "url": "#api-token-authentication", "type": "anchor", "status": "Anchor found: API Token Authentication"}, {"text": "SPA Authentication", "url": "#spa-authentication", "type": "anchor", "status": "Anchor found: SPA Authentication"}, {"text": "Mobile App Integration", "url": "#mobile-app-integration", "type": "anchor", "status": "Anchor found: Mobile App Integration"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON>", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: 070-laravel-fractal-guide.md"}, {"text": "Package Implementation Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "101-laravel-query-builder-guide.md": {"file": "101-laravel-query-builder-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Sorting & Pagination", "url": "#sorting--pagination", "type": "anchor", "status": "Anchor not found: #sorting--pagination"}, {"text": "Custom Filter Development", "url": "#custom-filter-development", "type": "anchor", "status": "Anchor not found: #custom-filter-development"}, {"text": "Real-World Examples", "url": "#real-world-examples", "type": "anchor", "status": "Anchor not found: #real-world-examples"}, {"text": "Spatie Comments System Guide", "url": "110-spatie-comments-guide.md", "type": "internal", "status": "File not found: 110-spatie-comments-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File not found: 090-laravel-workos-guide.md"}, {"text": "Laravel Folio Guide", "url": "120-laravel-folio-guide.md", "type": "internal", "status": "File not found: 120-laravel-folio-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "API Endpoint Design", "url": "#api-endpoint-design", "type": "anchor", "status": "Anchor found: API Endpoint Design"}, {"text": "Advanced Filtering Strategies", "url": "#advanced-filtering-strategies", "type": "anchor", "status": "Anchor found: Advanced Filtering Strategies"}, {"text": "Laravel Data Integration", "url": "#laravel-data-integration", "type": "anchor", "status": "Anchor found: Laravel Data Integration"}, {"text": "Security Considerations", "url": "#security-considerations", "type": "anchor", "status": "Anchor found: Security Considerations"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}]}, "151-spatie-activitylog-guide.md": {"file": "151-spatie-activitylog-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Security & Compliance", "url": "#security--compliance", "type": "anchor", "status": "Anchor not found: #security--compliance"}, {"text": "Real-time Activity Monitoring", "url": "#real-time-activity-monitoring", "type": "anchor", "status": "Anchor not found: #real-time-activity-monitoring"}, {"text": "Activity Analytics", "url": "#activity-analytics", "type": "anchor", "status": "Anchor not found: #activity-analytics"}, {"text": "Integration Patterns", "url": "#integration-patterns", "type": "anchor", "status": "Anchor not found: #integration-patterns"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor not found: #production-deployment"}, {"text": "Modern Testing with Pest Guide", "url": "../testing/010-pest-testing-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../testing/010-pest-testing-guide.md"}, {"text": "Development Debugging Tools Guide", "url": "../development/010-debugbar-guide.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/../development/010-debugbar-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File not found: 090-laravel-workos-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Advanced Activity Logging", "url": "#advanced-activity-logging", "type": "anchor", "status": "Anchor found: Advanced Activity Logging"}, {"text": "Custom Activity Models", "url": "#custom-activity-models", "type": "anchor", "status": "Anchor found: Custom Activity Models"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}]}, "091-laravel-workos-guide.md": {"file": "091-laravel-workos-guide.md", "total_links": 17, "internal_links": 3, "anchor_links": 12, "external_links": 2, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Monitoring & Troubleshooting", "url": "#monitoring--troubleshooting", "type": "anchor", "status": "Anchor not found: #monitoring--troubleshooting"}, {"text": "<PERSON><PERSON> Query Builder Guide", "url": "100-laravel-query-builder-guide.md", "type": "internal", "status": "File not found: 100-laravel-query-builder-guide.md"}, {"text": "Spatie Comments System Guide", "url": "110-spatie-comments-guide.md", "type": "internal", "status": "File not found: 110-spatie-comments-guide.md"}, {"text": "Laravel Folio Guide", "url": "120-laravel-folio-guide.md", "type": "internal", "status": "File not found: 120-laravel-folio-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "SSO Integration Patterns", "url": "#sso-integration-patterns", "type": "anchor", "status": "Anchor found: SSO Integration Patterns"}, {"text": "Directory Sync Implementation", "url": "#directory-sync-implementation", "type": "anchor", "status": "Anchor found: Directory Sync Implementation"}, {"text": "User Provisioning Workflows", "url": "#user-provisioning-workflows", "type": "anchor", "status": "Anchor found: User Provisioning Workflows"}, {"text": "RBAC Integration", "url": "#rbac-integration", "type": "anchor", "status": "Anchor found: RBAC Integration"}, {"text": "Multi-Tenant Architecture", "url": "#multi-tenant-architecture", "type": "anchor", "status": "Anchor found: Multi-Tenant Architecture"}, {"text": "Security Best Practices", "url": "#security-best-practices", "type": "anchor", "status": "Anchor found: Security Best Practices"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}, {"text": "Integration Examples", "url": "#integration-examples", "type": "anchor", "status": "Anchor found: Integration Examples"}, {"text": "WorkOS Laravel documentation", "url": "https://workos.com/docs/integrations/laravel", "type": "external", "status": "External link (not validated)"}, {"text": "spatie/laravel-permission documentation", "url": "https://spatie.be/docs/laravel-permission", "type": "external", "status": "External link (not validated)"}]}, "020-laravel-pulse-guide.md": {"file": "020-laravel-pulse-guide.md", "total_links": 24, "internal_links": 2, "anchor_links": 22, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Database Configuration", "url": "#12-database-configuration", "type": "anchor", "status": "Anchor not found: #12-database-configuration"}, {"text": "1.3. Environment Setup", "url": "#13-environment-setup", "type": "anchor", "status": "Anchor not found: #13-environment-setup"}, {"text": "2.1. Basic Dashboard Setup", "url": "#21-basic-dashboard-setup", "type": "anchor", "status": "Anchor not found: #21-basic-dashboard-setup"}, {"text": "2.2. Custom Dashboard Layouts", "url": "#22-custom-dashboard-layouts", "type": "anchor", "status": "Anchor not found: #22-custom-dashboard-layouts"}, {"text": "2.3. Authentication & Authorization", "url": "#23-authentication--authorization", "type": "anchor", "status": "Anchor not found: #23-authentication--authorization"}, {"text": "3.1. Built-in Recorders", "url": "#31-built-in-recorders", "type": "anchor", "status": "Anchor not found: #31-built-in-recorders"}, {"text": "3.2. Custom Metrics Collection", "url": "#32-custom-metrics-collection", "type": "anchor", "status": "Anchor not found: #32-custom-metrics-collection"}, {"text": "3.3. Performance Monitoring", "url": "#33-performance-monitoring", "type": "anchor", "status": "Anchor not found: #33-performance-monitoring"}, {"text": "Custom Metrics & Cards", "url": "#custom-metrics--cards", "type": "anchor", "status": "Anchor not found: #custom-metrics--cards"}, {"text": "4.1. Creating Custom Recorders", "url": "#41-creating-custom-recorders", "type": "anchor", "status": "Anchor not found: #41-creating-custom-recorders"}, {"text": "4.2. Building Custom Cards", "url": "#42-building-custom-cards", "type": "anchor", "status": "Anchor not found: #42-building-custom-cards"}, {"text": "4.3. Business Metrics Integration", "url": "#43-business-metrics-integration", "type": "anchor", "status": "Anchor not found: #43-business-metrics-integration"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Dashboard Configuration", "url": "#dashboard-configuration", "type": "anchor", "status": "Anchor found: Dashboard Configuration"}, {"text": "Data Collection Setup", "url": "#data-collection-setup", "type": "anchor", "status": "Anchor found: Data Collection Setup"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Integration Strategies", "url": "#integration-strategies", "type": "anchor", "status": "Anchor found: Integration Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON>up Guide", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: 010-laravel-backup-guide.md"}, {"text": "Laravel Telescope Guide", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: 030-laravel-telescope-guide.md"}]}, "121-laravel-folio-guide.md": {"file": "121-laravel-folio-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Livewire/Volt Integration", "url": "#livewire-volt-integration", "type": "anchor", "status": "Anchor not found: #livewire-volt-integration"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor not found: #production-deployment"}, {"text": "Real-World Examples", "url": "#real-world-examples", "type": "anchor", "status": "Anchor not found: #real-world-examples"}, {"text": "NNJeim World Guide", "url": "130-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File not found: 130-nnjeim-world-guide.md"}, {"text": "Laravel Database Optimization Guide", "url": "140-laravel-optimize-database-guide.md", "type": "internal", "status": "File not found: 140-laravel-optimize-database-guide.md"}, {"text": "Spatie Comments Guide", "url": "110-spatie-comments-guide.md", "type": "internal", "status": "File not found: 110-spatie-comments-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Page-based Routing Architecture", "url": "#page-based-routing-architecture", "type": "anchor", "status": "Anchor found: Page-based Routing Architecture"}, {"text": "Route Model Binding", "url": "#route-model-binding", "type": "anchor", "status": "Anchor found: Route Model Binding"}, {"text": "Middleware Integration", "url": "#middleware-integration", "type": "anchor", "status": "Anchor found: Middleware Integration"}, {"text": "SEO Optimization", "url": "#seo-optimization", "type": "anchor", "status": "Anchor found: SEO Optimization"}, {"text": "Performance Considerations", "url": "#performance-considerations", "type": "anchor", "status": "Anchor found: Performance Considerations"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}]}, "140-spatie-laravel-query-builder-guide.md": {"file": "140-spatie-laravel-query-builder-guide.md", "total_links": 11, "internal_links": 3, "anchor_links": 8, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Usage", "url": "#basic-usage", "type": "anchor", "status": "Anchor found: Basic Usage"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Integration with Chinook", "url": "#integration-with-chinook", "type": "anchor", "status": "Anchor found: Integration with Chinook"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "<PERSON><PERSON> Settings Guide", "url": "130-spatie-laravel-settings-guide.md", "type": "internal", "status": "File exists: 130-spatie-laravel-settings-guide.md"}, {"text": "<PERSON><PERSON> Translatable Guide", "url": "150-spatie-laravel-translatable-guide.md", "type": "internal", "status": "File exists: 150-spatie-laravel-translatable-guide.md"}, {"text": "Package Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "060-laravel-data-guide.md": {"file": "060-laravel-data-guide.md", "total_links": 25, "internal_links": 2, "anchor_links": 23, "external_links": 0, "broken_links": [{"text": "Installation & Setup", "url": "#installation--setup", "type": "anchor", "status": "Anchor not found: #installation--setup"}, {"text": "1.1. Package Installation", "url": "#11-package-installation", "type": "anchor", "status": "Anchor not found: #11-package-installation"}, {"text": "1.2. Configuration Publishing", "url": "#12-configuration-publishing", "type": "anchor", "status": "Anchor not found: #12-configuration-publishing"}, {"text": "1.3. Basic Setup", "url": "#13-basic-setup", "type": "anchor", "status": "Anchor not found: #13-basic-setup"}, {"text": "2.1. Basic DTO Creation", "url": "#21-basic-dto-creation", "type": "anchor", "status": "Anchor not found: #21-basic-dto-creation"}, {"text": "2.2. Advanced DTO Features", "url": "#22-advanced-dto-features", "type": "anchor", "status": "Anchor not found: #22-advanced-dto-features"}, {"text": "2.3. Nested DTOs", "url": "#23-nested-dtos", "type": "anchor", "status": "Anchor not found: #23-nested-dtos"}, {"text": "Validation & Transformation", "url": "#validation--transformation", "type": "anchor", "status": "Anchor not found: #validation--transformation"}, {"text": "3.1. Built-in Validation", "url": "#31-built-in-validation", "type": "anchor", "status": "Anchor not found: #31-built-in-validation"}, {"text": "3.2. Custom Validation Rules", "url": "#32-custom-validation-rules", "type": "anchor", "status": "Anchor not found: #32-custom-validation-rules"}, {"text": "3.3. Data Transformation", "url": "#33-data-transformation", "type": "anchor", "status": "Anchor not found: #33-data-transformation"}, {"text": "4.1. API Resource Integration", "url": "#41-api-resource-integration", "type": "anchor", "status": "Anchor not found: #41-api-resource-integration"}, {"text": "4.2. Request Handling", "url": "#42-request-handling", "type": "anchor", "status": "Anchor not found: #42-request-handling"}, {"text": "4.3. Response Formatting", "url": "#43-response-formatting", "type": "anchor", "status": "Anchor not found: #43-response-formatting"}, {"text": "Collections & Arrays", "url": "#collections--arrays", "type": "anchor", "status": "Anchor not found: #collections--arrays"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Data Transfer Objects", "url": "#data-transfer-objects", "type": "anchor", "status": "Anchor found: Data Transfer Objects"}, {"text": "API Integration", "url": "#api-integration", "type": "anchor", "status": "Anchor found: API Integration"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Advanced Patterns", "url": "#advanced-patterns", "type": "anchor", "status": "Anchor found: Advanced Patterns"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Laravel Horizon Guide", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: 050-laravel-horizon-guide.md"}, {"text": "<PERSON><PERSON>", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: 070-laravel-fractal-guide.md"}]}, "111-spatie-comments-guide.md": {"file": "111-spatie-comments-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "RBAC Integration", "url": "#rbac-integration", "type": "anchor", "status": "Anchor not found: #rbac-integration"}, {"text": "Real-time Updates", "url": "#real-time-updates", "type": "anchor", "status": "Anchor not found: #real-time-updates"}, {"text": "Spam Prevention", "url": "#spam-prevention", "type": "anchor", "status": "Anchor not found: #spam-prevention"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File not found: 090-laravel-workos-guide.md"}, {"text": "Laravel Query Builder Guide", "url": "100-laravel-query-builder-guide.md", "type": "internal", "status": "File not found: 100-laravel-query-builder-guide.md"}, {"text": "Laravel Folio Guide", "url": "120-laravel-folio-guide.md", "type": "internal", "status": "File not found: 120-laravel-folio-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Comment System Architecture", "url": "#comment-system-architecture", "type": "anchor", "status": "Anchor found: Comment System Architecture"}, {"text": "Livewire Integration", "url": "#livewire-integration", "type": "anchor", "status": "Anchor found: Livewire Integration"}, {"text": "Moderation Workflows", "url": "#moderation-workflows", "type": "anchor", "status": "Anchor found: Moderation Workflows"}, {"text": "Notification Systems", "url": "#notification-systems", "type": "anchor", "status": "Anchor found: Notification Systems"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing Strategies", "url": "#testing-strategies", "type": "anchor", "status": "Anchor found: Testing Strategies"}, {"text": "Production Deployment", "url": "#production-deployment", "type": "anchor", "status": "Anchor found: Production Deployment"}]}, "testing/000-testing-index.md": {"file": "testing/000-testing-index.md", "total_links": 12, "internal_links": 6, "anchor_links": 0, "external_links": 6, "broken_links": [{"text": "Development Tools Index", "url": "../development/000-development-index.md", "type": "internal", "status": "File not found: development/000-development-index.md"}, {"text": "Main Chinook Index", "url": "../../000-chinook-index.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/packages/testing/../../000-chinook-index.md"}], "working_links": [{"text": "Pest Testing Guide", "url": "010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Pest Testing Guide", "url": "010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Pest PHP Documentation", "url": "https://pestphp.com/", "type": "external", "status": "External link (not validated)"}, {"text": "Laravel Testing Documentation", "url": "https://laravel.com/docs/testing", "type": "external", "status": "External link (not validated)"}, {"text": "PHPUnit Documentation", "url": "https://phpunit.de/documentation.html", "type": "external", "status": "External link (not validated)"}, {"text": "Laravel Testing Examples", "url": "https://github.com/laravel/framework/tree/master/tests", "type": "external", "status": "External link (not validated)"}, {"text": "Pest Plugin Ecosystem", "url": "https://pestphp.com/docs/plugins", "type": "external", "status": "External link (not validated)"}, {"text": "Testing Best Practices", "url": "https://martinfowler.com/testing/", "type": "external", "status": "External link (not validated)"}, {"text": "Pest Testing Guide", "url": "010-pest-testing-guide.md", "type": "internal", "status": "File exists: testing/010-pest-testing-guide.md"}, {"text": "Package Documentation Index", "url": "../000-packages-index.md", "type": "internal", "status": "File exists: 000-packages-index.md"}]}, "testing/010-pest-testing-guide.md": {"file": "testing/010-pest-testing-guide.md", "total_links": 15, "internal_links": 3, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Installation & Configuration", "url": "#installation--configuration", "type": "anchor", "status": "Anchor not found: #installation--configuration"}, {"text": "Advanced Testing Patterns", "url": "#advanced-testing-patterns", "type": "anchor", "status": "Anchor not found: #advanced-testing-patterns"}, {"text": "Type Coverage", "url": "#type-coverage", "type": "anchor", "status": "Anchor not found: #type-coverage"}, {"text": "API Testing", "url": "#api-testing", "type": "anchor", "status": "Anchor not found: #api-testing"}, {"text": "Livewire Testing", "url": "#livewire-testing", "type": "anchor", "status": "Anchor not found: #livewire-testing"}, {"text": "Development Debugging Tools Guide", "url": "../development/010-debugbar-guide.md", "type": "internal", "status": "File not found: development/010-debugbar-guide.md"}, {"text": "Code Quality and Formatting Guide", "url": "../development/020-pint-code-quality-guide.md", "type": "internal", "status": "File not found: development/020-pint-code-quality-guide.md"}, {"text": "Enhanced Spatie ActivityLog Guide", "url": "../packages/150-spatie-activitylog-guide.md", "type": "internal", "status": "File not found: packages/150-spatie-activitylog-guide.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Test Architecture", "url": "#test-architecture", "type": "anchor", "status": "Anchor found: Test Architecture"}, {"text": "Plugin Ecosystem", "url": "#plugin-ecosystem", "type": "anchor", "status": "Anchor found: Plugin Ecosystem"}, {"text": "Performance Testing", "url": "#performance-testing", "type": "anchor", "status": "Anchor found: Performance Testing"}, {"text": "CI/CD Integration", "url": "#cicd-integration", "type": "anchor", "status": "Anchor found: CI/CD Integration"}, {"text": "Database Testing", "url": "#database-testing", "type": "anchor", "status": "Anchor found: Database Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}]}}}