{"metadata": {"timestamp": "2025-07-08T12:28:16.959239", "total_files": 17, "total_links": 301, "broken_links": 1, "success_rate": 99.66777408637874, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "000-index.md", "link": "#sales--invoicing", "text": "Sales & Invoicing", "status": "Anchor not found: #sales--invoicing"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.07744503021240234}, "detailed_results": {"030-tracks-resource.md": {"file": "030-tracks-resource.md", "total_links": 23, "internal_links": 2, "anchor_links": 21, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Implementation", "url": "#resource-implementation", "type": "anchor", "status": "Anchor found: Resource Implementation"}, {"text": "Basic Resource Structure", "url": "#basic-resource-structure", "type": "anchor", "status": "Anchor found: Basic Resource Structure"}, {"text": "Form Configuration", "url": "#form-configuration", "type": "anchor", "status": "Anchor found: Form Configuration"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Complex Relationships", "url": "#complex-relationships", "type": "anchor", "status": "Anchor found: Complex Relationships"}, {"text": "Album Relationship", "url": "#album-relationship", "type": "anchor", "status": "Anchor found: Album Relationship"}, {"text": "Media Type Relationship", "url": "#media-type-relationship", "type": "anchor", "status": "Anchor found: Media Type Relationship"}, {"text": "Invoice Lines Relationship", "url": "#invoice-lines-relationship", "type": "anchor", "status": "Anchor found: Invoice Lines Relationship"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Audio File Management", "url": "#audio-file-management", "type": "anchor", "status": "Anchor found: Audio File Management"}, {"text": "Pricing and Sales", "url": "#pricing-and-sales", "type": "anchor", "status": "Anchor found: Pricing and Sales"}, {"text": "Playlist Integration", "url": "#playlist-integration", "type": "anchor", "status": "Anchor found: Playlist Integration"}, {"text": "Business Logic", "url": "#business-logic", "type": "anchor", "status": "Anchor found: Business Logic"}, {"text": "Track Validation", "url": "#track-validation", "type": "anchor", "status": "Anchor found: Track Validation"}, {"text": "Duration Handling", "url": "#duration-handling", "type": "anchor", "status": "Anchor found: Duration Handling"}, {"text": "Sales Analytics", "url": "#sales-analytics", "type": "anchor", "status": "Anchor found: Sales Analytics"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Authorization", "url": "#authorization", "type": "anchor", "status": "Anchor found: Authorization"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Albums Resource", "url": "020-albums-resource.md", "type": "internal", "status": "File exists: 020-albums-resource.md"}, {"text": "Categories Resource", "url": "040-categories-resource.md", "type": "internal", "status": "File exists: 040-categories-resource.md"}]}, "140-bulk-operations.md": {"file": "140-bulk-operations.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Bulk Actions", "url": "#basic-bulk-actions", "type": "anchor", "status": "Anchor found: Basic Bulk Actions"}, {"text": "Advanced Bulk Operations", "url": "#advanced-bulk-operations", "type": "anchor", "status": "Anchor found: Advanced Bulk Operations"}, {"text": "Custom Bulk Actions", "url": "#custom-bulk-actions", "type": "anchor", "status": "Anchor found: Custom Bulk Actions"}, {"text": "Conditional Bulk Actions", "url": "#conditional-bulk-actions", "type": "anchor", "status": "Anchor found: Conditional Bulk Actions"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Erro<PERSON>", "url": "#error-handling", "type": "anchor", "status": "Anchor found: <PERSON><PERSON><PERSON>"}, {"text": "Progress Tracking", "url": "#progress-tracking", "type": "anchor", "status": "Anchor found: Progress Tracking"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Relationship Managers Guide", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}]}, "120-form-components.md": {"file": "120-form-components.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Form Components", "url": "#basic-form-components", "type": "anchor", "status": "Anchor found: Basic Form Components"}, {"text": "Advanced Input Components", "url": "#advanced-input-components", "type": "anchor", "status": "Anchor found: Advanced Input Components"}, {"text": "Custom Form Components", "url": "#custom-form-components", "type": "anchor", "status": "Anchor found: Custom Form Components"}, {"text": "Validation Patterns", "url": "#validation-patterns", "type": "anchor", "status": "Anchor found: <PERSON><PERSON><PERSON> Pat<PERSON>s"}, {"text": "Dynamic Form Behavior", "url": "#dynamic-form-behavior", "type": "anchor", "status": "Anchor found: Dynamic Form Behavior"}, {"text": "File Upload Components", "url": "#file-upload-components", "type": "anchor", "status": "Anchor found: File Upload Components"}, {"text": "Relationship Components", "url": "#relationship-components", "type": "anchor", "status": "Anchor found: Relationship Components"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Users Resource Guide", "url": "110-users-resource.md", "type": "internal", "status": "File exists: 110-users-resource.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Bulk Operations Guide", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}]}, "080-invoices-resource.md": {"file": "080-invoices-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Payment Processing", "url": "#payment-processing", "type": "anchor", "status": "Anchor found: Payment Processing"}, {"text": "Invoice Management", "url": "#invoice-management", "type": "anchor", "status": "Anchor found: Invoice Management"}, {"text": "Financial Analytics", "url": "#financial-analytics", "type": "anchor", "status": "Anchor found: Financial Analytics"}, {"text": "Tax and Compliance", "url": "#tax-and-compliance", "type": "anchor", "status": "Anchor found: Tax and Compliance"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Customers Resource Guide", "url": "070-customers-resource.md", "type": "internal", "status": "File exists: 070-customers-resource.md"}, {"text": "Invoice Lines Resource Guide", "url": "090-invoice-lines-resource.md", "type": "internal", "status": "File exists: 090-invoice-lines-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}]}, "120-relationship-managers.md": {"file": "120-relationship-managers.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Relationship Managers", "url": "#basic-relationship-managers", "type": "anchor", "status": "Anchor found: Basic Relationship Managers"}, {"text": "Artist Relationship Managers", "url": "#artist-relationship-managers", "type": "anchor", "status": "Anchor found: Artist Relationship Managers"}, {"text": "Album Relationship Managers", "url": "#album-relationship-managers", "type": "anchor", "status": "Anchor found: Album Relationship Managers"}, {"text": "Category Relationship Managers", "url": "#category-relationship-managers", "type": "anchor", "status": "Anchor found: Category Relationship Managers"}, {"text": "Advanced Relationship Patterns", "url": "#advanced-relationship-patterns", "type": "anchor", "status": "Anchor found: Advanced Relationship Patterns"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Authorization and Security", "url": "#authorization-and-security", "type": "anchor", "status": "Anchor found: Authorization and Security"}, {"text": "Testing Relationship Managers", "url": "#testing-relationship-managers", "type": "anchor", "status": "Anchor found: Testing Relationship Managers"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Users Resource", "url": "110-users-resource.md", "type": "internal", "status": "File exists: 110-users-resource.md"}, {"text": "Form Components", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}]}, "070-customers-resource.md": {"file": "070-customers-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Customer Analytics", "url": "#customer-analytics", "type": "anchor", "status": "Anchor found: Customer Analytics"}, {"text": "Purchase History Management", "url": "#purchase-history-management", "type": "anchor", "status": "Anchor found: Purchase History Management"}, {"text": "Communication Tools", "url": "#communication-tools", "type": "anchor", "status": "Anchor found: Communication Tools"}, {"text": "Privacy and Security", "url": "#privacy-and-security", "type": "anchor", "status": "Anchor found: Privacy and Security"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Media Types Resource Guide", "url": "060-media-types-resource.md", "type": "internal", "status": "File exists: 060-media-types-resource.md"}, {"text": "Invoices Resource Guide", "url": "080-invoices-resource.md", "type": "internal", "status": "File exists: 080-invoices-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}]}, "README.md": {"file": "README.md", "total_links": 19, "internal_links": 19, "anchor_links": 0, "external_links": 0, "broken_links": [], "working_links": [{"text": "Artists Resource", "url": "010-artists-resource.md", "type": "internal", "status": "File exists: 010-artists-resource.md"}, {"text": "Albums Resource", "url": "020-albums-resource.md", "type": "internal", "status": "File exists: 020-albums-resource.md"}, {"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: 030-tracks-resource.md"}, {"text": "Categories Resource", "url": "040-categories-resource.md", "type": "internal", "status": "File exists: 040-categories-resource.md"}, {"text": "Playlists Resource", "url": "050-playlists-resource.md", "type": "internal", "status": "File exists: 050-playlists-resource.md"}, {"text": "Media Types Resource", "url": "060-media-types-resource.md", "type": "internal", "status": "File exists: 060-media-types-resource.md"}, {"text": "Customers Resource", "url": "070-customers-resource.md", "type": "internal", "status": "File exists: 070-customers-resource.md"}, {"text": "Invoices Resource", "url": "080-invoices-resource.md", "type": "internal", "status": "File exists: 080-invoices-resource.md"}, {"text": "Invoice Lines Resource", "url": "090-invoice-lines-resource.md", "type": "internal", "status": "File exists: 090-invoice-lines-resource.md"}, {"text": "Employees Resource", "url": "100-employees-resource.md", "type": "internal", "status": "File exists: 100-employees-resource.md"}, {"text": "Users Resource", "url": "110-users-resource.md", "type": "internal", "status": "File exists: 110-users-resource.md"}, {"text": "Relationship Managers", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}, {"text": "Form Components", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Bulk Operations", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}, {"text": "Resource Index", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Bulk Operations Guide", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}]}, "020-albums-resource.md": {"file": "020-albums-resource.md", "total_links": 24, "internal_links": 2, "anchor_links": 22, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Implementation", "url": "#resource-implementation", "type": "anchor", "status": "Anchor found: Resource Implementation"}, {"text": "Basic Resource Structure", "url": "#basic-resource-structure", "type": "anchor", "status": "Anchor found: Basic Resource Structure"}, {"text": "Form Configuration", "url": "#form-configuration", "type": "anchor", "status": "Anchor found: Form Configuration"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Relationship Managers", "url": "#relationship-managers", "type": "anchor", "status": "Anchor found: Relationship Managers"}, {"text": "Tracks Relationship Manager", "url": "#tracks-relationship-manager", "type": "anchor", "status": "Anchor found: Tracks Relationship Manager"}, {"text": "Categories Relationship Manager", "url": "#categories-relationship-manager", "type": "anchor", "status": "Anchor found: Categories Relationship Manager"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Custom Form Components", "url": "#custom-form-components", "type": "anchor", "status": "Anchor found: Custom Form Components"}, {"text": "Advanced Table Features", "url": "#advanced-table-features", "type": "anchor", "status": "Anchor found: Advanced Table Features"}, {"text": "Bulk Operations", "url": "#bulk-operations", "type": "anchor", "status": "Anchor found: Bulk Operations"}, {"text": "Authorization", "url": "#authorization", "type": "anchor", "status": "Anchor found: Authorization"}, {"text": "Resource-Level Authorization", "url": "#resource-level-authorization", "type": "anchor", "status": "Anchor found: Resource-Level Authorization"}, {"text": "Field-Level Security", "url": "#field-level-security", "type": "anchor", "status": "Anchor found: Field-Level Security"}, {"text": "Business Logic", "url": "#business-logic", "type": "anchor", "status": "Anchor found: Business Logic"}, {"text": "Album Validation", "url": "#album-validation", "type": "anchor", "status": "Anchor found: Album Validation"}, {"text": "Release Date Handling", "url": "#release-date-handling", "type": "anchor", "status": "Anchor found: Release Date Handling"}, {"text": "Cover Art Management", "url": "#cover-art-management", "type": "anchor", "status": "Anchor found: Cover Art Management"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Artists Resource", "url": "010-artists-resource.md", "type": "internal", "status": "File exists: 010-artists-resource.md"}, {"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: 030-tracks-resource.md"}]}, "040-categories-resource.md": {"file": "040-categories-resource.md", "total_links": 26, "internal_links": 2, "anchor_links": 24, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Implementation", "url": "#resource-implementation", "type": "anchor", "status": "Anchor found: Resource Implementation"}, {"text": "Basic Resource Structure", "url": "#basic-resource-structure", "type": "anchor", "status": "Anchor found: Basic Resource Structure"}, {"text": "Hierarchical Form Configuration", "url": "#hierarchical-form-configuration", "type": "anchor", "status": "Anchor found: Hierarchical Form Configuration"}, {"text": "Tree Table Configuration", "url": "#tree-table-configuration", "type": "anchor", "status": "Anchor found: Tree Table Configuration"}, {"text": "Hierarchical Data Management", "url": "#hierarchical-data-management", "type": "anchor", "status": "Anchor found: Hierarchical Data Management"}, {"text": "Hybrid Architecture", "url": "#hybrid-architecture", "type": "anchor", "status": "Anchor found: Hybrid Architecture"}, {"text": "Tree Operations", "url": "#tree-operations", "type": "anchor", "status": "Anchor found: Tree Operations"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Polymorphic Relationships", "url": "#polymorphic-relationships", "type": "anchor", "status": "Anchor found: Polymorphic Relationships"}, {"text": "Categorizable Implementation", "url": "#categorizable-implementation", "type": "anchor", "status": "Anchor found: Categorizable Implementation"}, {"text": "Multi-Model Assignment", "url": "#multi-model-assignment", "type": "anchor", "status": "Anchor found: Multi-Model Assignment"}, {"text": "Category Types", "url": "#category-types", "type": "anchor", "status": "Anchor found: Category Types"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Category Tree Visualization", "url": "#category-tree-visualization", "type": "anchor", "status": "Anchor found: Category Tree Visualization"}, {"text": "Bulk Category Operations", "url": "#bulk-category-operations", "type": "anchor", "status": "Anchor found: Bulk Category Operations"}, {"text": "Category Analytics", "url": "#category-analytics", "type": "anchor", "status": "Anchor found: Category Analytics"}, {"text": "Business Logic", "url": "#business-logic", "type": "anchor", "status": "Anchor found: Business Logic"}, {"text": "Category Validation", "url": "#category-validation", "type": "anchor", "status": "Anchor found: Category Validation"}, {"text": "Hierarchy Constraints", "url": "#hierarchy-constraints", "type": "anchor", "status": "Anchor found: Hierarchy Constraints"}, {"text": "Usage Tracking", "url": "#usage-tracking", "type": "anchor", "status": "Anchor found: Usage Tracking"}, {"text": "Authorization", "url": "#authorization", "type": "anchor", "status": "Anchor found: Authorization"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: 030-tracks-resource.md"}, {"text": "Playlists Resource", "url": "050-playlists-resource.md", "type": "internal", "status": "File exists: 050-playlists-resource.md"}]}, "130-table-features.md": {"file": "130-table-features.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Basic Table Configuration", "url": "#basic-table-configuration", "type": "anchor", "status": "Anchor found: Basic Table Configuration"}, {"text": "Advanced Column Types", "url": "#advanced-column-types", "type": "anchor", "status": "Anchor found: Advanced Column Types"}, {"text": "Custom Table Actions", "url": "#custom-table-actions", "type": "anchor", "status": "Anchor found: Custom Table Actions"}, {"text": "Filtering and Search", "url": "#filtering-and-search", "type": "anchor", "status": "Anchor found: Filtering and Search"}, {"text": "Sorting and Grouping", "url": "#sorting-and-grouping", "type": "anchor", "status": "Anchor found: Sorting and Grouping"}, {"text": "Bulk Operations", "url": "#bulk-operations", "type": "anchor", "status": "Anchor found: Bulk Operations"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Bulk Operations Guide", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Bulk Operations Guide", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}]}, "050-playlists-resource.md": {"file": "050-playlists-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Relationship Management", "url": "#relationship-management", "type": "anchor", "status": "Anchor found: Relationship Management"}, {"text": "Actions and Bulk Actions", "url": "#actions-and-bulk-actions", "type": "anchor", "status": "Anchor found: Actions and Bulk Actions"}, {"text": "Permissions and Policies", "url": "#permissions-and-policies", "type": "anchor", "status": "Anchor found: Permissions and Policies"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Categories Resource Guide", "url": "040-categories-resource.md", "type": "internal", "status": "File exists: 040-categories-resource.md"}, {"text": "Media Types Resource Guide", "url": "060-media-types-resource.md", "type": "internal", "status": "File exists: 060-media-types-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Relationship Managers", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}, {"text": "Form Components", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}]}, "090-invoice-lines-resource.md": {"file": "090-invoice-lines-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Line Item Management", "url": "#line-item-management", "type": "anchor", "status": "Anchor found: Line Item Management"}, {"text": "Pricing and Discounts", "url": "#pricing-and-discounts", "type": "anchor", "status": "Anchor found: Pricing and Discounts"}, {"text": "Inventory Integration", "url": "#inventory-integration", "type": "anchor", "status": "Anchor found: Inventory Integration"}, {"text": "Analytics and Reporting", "url": "#analytics-and-reporting", "type": "anchor", "status": "Anchor found: Analytics and Reporting"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Invoices Resource Guide", "url": "080-invoices-resource.md", "type": "internal", "status": "File exists: 080-invoices-resource.md"}, {"text": "Employees Resource Guide", "url": "100-employees-resource.md", "type": "internal", "status": "File exists: 100-employees-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}]}, "100-employees-resource.md": {"file": "100-employees-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Role Management", "url": "#role-management", "type": "anchor", "status": "Anchor found: Role Management"}, {"text": "Performance Tracking", "url": "#performance-tracking", "type": "anchor", "status": "Anchor found: Performance Tracking"}, {"text": "HR Integration", "url": "#hr-integration", "type": "anchor", "status": "Anchor found: HR Integration"}, {"text": "Security and Privacy", "url": "#security-and-privacy", "type": "anchor", "status": "Anchor found: Security and Privacy"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Invoice Lines Resource Guide", "url": "090-invoice-lines-resource.md", "type": "internal", "status": "File exists: 090-invoice-lines-resource.md"}, {"text": "Users Resource Guide", "url": "110-users-resource.md", "type": "internal", "status": "File exists: 110-users-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}]}, "110-users-resource.md": {"file": "110-users-resource.md", "total_links": 16, "internal_links": 5, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Authentication Management", "url": "#authentication-management", "type": "anchor", "status": "Anchor found: Authentication Management"}, {"text": "Role and Permission Management", "url": "#role-and-permission-management", "type": "anchor", "status": "Anchor found: Role and Permission Management"}, {"text": "User Activity Tracking", "url": "#user-activity-tracking", "type": "anchor", "status": "Anchor found: User Activity Tracking"}, {"text": "Security Features", "url": "#security-features", "type": "anchor", "status": "Anchor found: Security Features"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Employees Resource Guide", "url": "100-employees-resource.md", "type": "internal", "status": "File exists: 100-employees-resource.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Relationship Managers Guide", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}]}, "000-index.md": {"file": "000-index.md", "total_links": 27, "internal_links": 17, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Sales & Invoicing", "url": "#sales--invoicing", "type": "anchor", "status": "Anchor not found: #sales--invoicing"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Documentation Structure", "url": "#documentation-structure", "type": "anchor", "status": "Anchor found: Documentation Structure"}, {"text": "Core Music Resources", "url": "#core-music-resources", "type": "anchor", "status": "Anchor found: Core Music Resources"}, {"text": "Category System", "url": "#category-system", "type": "anchor", "status": "Anchor found: Category System"}, {"text": "Customer Management", "url": "#customer-management", "type": "anchor", "status": "Anchor found: Customer Management"}, {"text": "Resource Architecture", "url": "#resource-architecture", "type": "anchor", "status": "Anchor found: Resource Architecture"}, {"text": "Implementation Patterns", "url": "#implementation-patterns", "type": "anchor", "status": "Anchor found: Implementation Patterns"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Artists Resource", "url": "010-artists-resource.md", "type": "internal", "status": "File exists: 010-artists-resource.md"}, {"text": "Albums Resource", "url": "020-albums-resource.md", "type": "internal", "status": "File exists: 020-albums-resource.md"}, {"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: 030-tracks-resource.md"}, {"text": "Categories Resource", "url": "040-categories-resource.md", "type": "internal", "status": "File exists: 040-categories-resource.md"}, {"text": "Customers Resource", "url": "070-customers-resource.md", "type": "internal", "status": "File exists: 070-customers-resource.md"}, {"text": "Employees Resource", "url": "100-employees-resource.md", "type": "internal", "status": "File exists: 100-employees-resource.md"}, {"text": "Users Resource", "url": "110-users-resource.md", "type": "internal", "status": "File exists: 110-users-resource.md"}, {"text": "Invoices Resource", "url": "080-invoices-resource.md", "type": "internal", "status": "File exists: 080-invoices-resource.md"}, {"text": "Invoice Lines Resource", "url": "090-invoice-lines-resource.md", "type": "internal", "status": "File exists: 090-invoice-lines-resource.md"}, {"text": "Playlists Resource", "url": "050-playlists-resource.md", "type": "internal", "status": "File exists: 050-playlists-resource.md"}, {"text": "Media Types Resource", "url": "060-media-types-resource.md", "type": "internal", "status": "File exists: 060-media-types-resource.md"}, {"text": "Form Components Guide", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}, {"text": "Bulk Operations Guide", "url": "140-bulk-operations.md", "type": "internal", "status": "File exists: 140-bulk-operations.md"}, {"text": "Relationship Managers Guide", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}, {"text": "Filament Resources Documentation", "url": "README.md", "type": "internal", "status": "File exists: README.md"}, {"text": "Artists Resource", "url": "010-artists-resource.md", "type": "internal", "status": "File exists: 010-artists-resource.md"}]}, "010-artists-resource.md": {"file": "010-artists-resource.md", "total_links": 11, "internal_links": 3, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "Resource Overview", "url": "#resource-overview", "type": "anchor", "status": "Anchor found: Resource Overview"}, {"text": "Model Requirements", "url": "#model-requirements", "type": "anchor", "status": "Anchor found: Model Requirements"}, {"text": "Form Implementation", "url": "#form-implementation", "type": "anchor", "status": "Anchor found: Form Implementation"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Relationship Managers", "url": "#relationship-managers", "type": "anchor", "status": "Anchor found: Relationship Managers"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "Authorization", "url": "#authorization", "type": "anchor", "status": "Anchor found: Authorization"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Albums Resource", "url": "020-albums-resource.md", "type": "internal", "status": "File exists: 020-albums-resource.md"}, {"text": "Categories Resource", "url": "040-categories-resource.md", "type": "internal", "status": "File exists: 040-categories-resource.md"}, {"text": "Relationship Managers", "url": "120-relationship-managers.md", "type": "internal", "status": "File exists: 120-relationship-managers.md"}]}, "060-media-types-resource.md": {"file": "060-media-types-resource.md", "total_links": 15, "internal_links": 5, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Resource Configuration", "url": "#resource-configuration", "type": "anchor", "status": "Anchor found: Resource Configuration"}, {"text": "Form Components", "url": "#form-components", "type": "anchor", "status": "Anchor found: Form Components"}, {"text": "Table Configuration", "url": "#table-configuration", "type": "anchor", "status": "Anchor found: Table Configuration"}, {"text": "Advanced Features", "url": "#advanced-features", "type": "anchor", "status": "Anchor found: Advanced Features"}, {"text": "File Type Management", "url": "#file-type-management", "type": "anchor", "status": "Anchor found: File Type Management"}, {"text": "Validation and Security", "url": "#validation-and-security", "type": "anchor", "status": "Anchor found: Validation and Security"}, {"text": "Testing", "url": "#testing", "type": "anchor", "status": "Anchor found: Testing"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Playlists Resource Guide", "url": "050-playlists-resource.md", "type": "internal", "status": "File exists: 050-playlists-resource.md"}, {"text": "Customers Resource Guide", "url": "070-customers-resource.md", "type": "internal", "status": "File exists: 070-customers-resource.md"}, {"text": "Resource Architecture", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}, {"text": "Form Components", "url": "120-form-components.md", "type": "internal", "status": "File exists: 120-form-components.md"}, {"text": "Table Features Guide", "url": "130-table-features.md", "type": "internal", "status": "File exists: 130-table-features.md"}]}}}