# Automated Link Validation Report

**Status:** ❌ FAIL
**Timestamp:** 2025-07-08T06:03:28.820863
**Execution Time:** 0.64 seconds

## Summary

- **Total Files:** 165
- **Total Links:** 3111
- **Broken Links:** 397
- **Success Rate:** 87.2%

## Critical Files Status

| File | Broken Links | Status |
|------|--------------|--------|
| 000-chinook-index.md | 18 | ❌ Major Issues |
| 050-chinook-advanced-features-guide.md | 1 | ⚠️ Minor Issues |
| 060-chinook-media-library-guide.md | 0 | ✅ Perfect |
| 070-chinook-hierarchy-comparison-guide.md | 0 | ✅ Perfect |
| filament/setup/000-index.md | 2 | ⚠️ Minor Issues |
| filament/resources/000-index.md | 1 | ⚠️ Minor Issues |
| packages/000-packages-index.md | 19 | ❌ Major Issues |
| testing/000-testing-index.md | 0 | ✅ Perfect |

## New Issues (397)

- **040-chinook-seeders-guide.md:** Customer Seeder → #customer-seeder
- **040-chinook-seeders-guide.md:** Playlist Seeder → #playlist-seeder
- **040-chinook-seeders-guide.md:** Track Seeder → #track-seeder
- **040-chinook-seeders-guide.md:** Invoice Seeder → #invoice-seeder
- **040-chinook-seeders-guide.md:** InvoiceLine Seeder → #invoiceline-seeder
- **040-chinook-seeders-guide.md:** PlaylistTrack Seeder → #playlisttrack-seeder
- **040-chinook-seeders-guide.md:** Database Seeder → #database-seeder
- **README.md:** Documentation Audit Report → reports/DOCUMENTATION_AUDIT_REPORT.md
- **000-chinook-index.md:** 8. Panel Setup & Configuration → #8-panel-setup--configuration
- **000-chinook-index.md:** 9. Model Standards & Architecture → #9-model-standards--architecture

*... and 387 more*

## Recommendations

🚨 **Immediate Action Required:** High number of broken links detected.
