# Automated Link Validation Report

**Status:** ❌ FAIL
**Timestamp:** 2025-07-08T05:42:38.282207
**Execution Time:** 0.11 seconds

## Summary

- **Total Files:** 25
- **Total Links:** 496
- **Broken Links:** 228
- **Success Rate:** 54.0%

## Critical Files Status

| File | Broken Links | Status |
|------|--------------|--------|
| 000-chinook-index.md | N/A | ❌ Missing |
| 050-chinook-advanced-features-guide.md | N/A | ❌ Missing |
| 060-chinook-media-library-guide.md | N/A | ❌ Missing |
| 070-chinook-hierarchy-comparison-guide.md | N/A | ❌ Missing |
| filament/setup/000-index.md | N/A | ❌ Missing |
| filament/resources/000-index.md | N/A | ❌ Missing |
| packages/000-packages-index.md | N/A | ❌ Missing |
| testing/000-testing-index.md | 2 | ⚠️ Minor Issues |

## New Issues (228)

- **030-laravel-telescope-guide.md:** Installation & Setup → #installation--setup
- **030-laravel-telescope-guide.md:** 1.1. Package Installation → #11-package-installation
- **030-laravel-telescope-guide.md:** 1.2. Configuration Publishing → #12-configuration-publishing
- **030-laravel-telescope-guide.md:** 1.3. Environment Configuration → #13-environment-configuration
- **030-laravel-telescope-guide.md:** Authorization & Security → #authorization--security
- **030-laravel-telescope-guide.md:** 2.1. Gate-Based Authorization → #21-gate-based-authorization
- **030-laravel-telescope-guide.md:** 2.2. Environment-Specific Access → #22-environment-specific-access
- **030-laravel-telescope-guide.md:** 2.3. IP Whitelisting → #23-ip-whitelisting
- **030-laravel-telescope-guide.md:** 3.1. Watcher Configuration → #31-watcher-configuration
- **030-laravel-telescope-guide.md:** 3.2. Filtering & Sampling → #32-filtering--sampling

*... and 218 more*

## Recommendations

🚨 **Immediate Action Required:** High number of broken links detected.
