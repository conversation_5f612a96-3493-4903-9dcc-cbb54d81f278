{"metadata": {"timestamp": "2025-07-08T02:20:35.620251", "total_files": 18, "total_links": 260, "broken_links": 43, "success_rate": 83.46153846153847, "critical_files_status": {"000-chinook-index.md": -1, "050-chinook-advanced-features-guide.md": -1, "060-chinook-media-library-guide.md": -1, "070-chinook-hierarchy-comparison-guide.md": -1, "filament/setup/000-index.md": -1, "filament/resources/000-index.md": -1, "packages/000-packages-index.md": -1, "testing/000-testing-index.md": -1}, "new_issues": [{"file": "040-ssl-configuration.md", "link": "#lets-encrypt-setup", "text": "Let's Encrypt Setup", "status": "Anchor not found: #lets-encrypt-setup"}, {"file": "100-logging-configuration.md", "link": "#log-channels--drivers", "text": "Log Channels & Drivers", "status": "Anchor not found: #log-channels--drivers"}, {"file": "100-logging-configuration.md", "link": "#log-rotation--retention", "text": "Log Rotation & Retention", "status": "Anchor not found: #log-rotation--retention"}, {"file": "030-security-hardening.md", "link": "#csrf-protection", "text": "CSRF Protection", "status": "Anchor not found: #csrf-protection"}, {"file": "030-security-hardening.md", "link": "#input-validation", "text": "Input Validation", "status": "Anchor not found: #input-validation"}, {"file": "030-security-hardening.md", "link": "#monitoring-and-logging", "text": "Monitoring and Logging", "status": "Anchor not found: #monitoring-and-logging"}, {"file": "030-security-hardening.md", "link": "#security-auditing", "text": "Security Auditing", "status": "Anchor not found: #security-auditing"}, {"file": "130-cicd-pipeline.md", "link": "#build--deployment", "text": "Build & Deployment", "status": "Anchor not found: #build--deployment"}, {"file": "150-performance-optimization-guide.md", "link": "#monitoring--profiling", "text": "Monitoring & Profiling", "status": "Anchor not found: #monitoring--profiling"}, {"file": "150-performance-optimization-guide.md", "link": "140-browser-testing.md", "text": "Browser Testing Guide", "status": "File not found: 140-browser-testing.md"}, {"file": "090-monitoring-setup.md", "link": "#error-tracking--logging", "text": "Error Tracking & Logging", "status": "Anchor not found: #error-tracking--logging"}, {"file": "090-monitoring-setup.md", "link": "#health-checks--uptime-monitoring", "text": "Health Checks & Uptime Monitoring", "status": "Anchor not found: #health-checks--uptime-monitoring"}, {"file": "090-monitoring-setup.md", "link": "#alerting--notifications", "text": "Alerting & Notifications", "status": "Anchor not found: #alerting--notifications"}, {"file": "README.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: 150-cloud-deployment.md"}, {"file": "README.md", "link": "../setup/", "text": "Setup Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../setup"}, {"file": "README.md", "link": "../resources/", "text": "Resources Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../resources"}, {"file": "README.md", "link": "../features/", "text": "Features Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../features"}, {"file": "README.md", "link": "../testing/", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../testing"}, {"file": "140-docker-deployment.md", "link": "#multi-stage-builds", "text": "Multi-Stage Builds", "status": "Anchor not found: #multi-stage-builds"}, {"file": "140-docker-deployment.md", "link": "#production-configuration", "text": "Production Configuration", "status": "Anchor not found: #production-configuration"}, {"file": "140-docker-deployment.md", "link": "#kubernetes-deployment", "text": "Kubernetes Deployment", "status": "Anchor not found: #kubernetes-deployment"}, {"file": "140-docker-deployment.md", "link": "#container-orchestration", "text": "Container Orchestration", "status": "Anchor not found: #container-orchestration"}, {"file": "140-docker-deployment.md", "link": "#monitoring--logging", "text": "Monitoring & Logging", "status": "Anchor not found: #monitoring--logging"}, {"file": "140-docker-deployment.md", "link": "#security-considerations", "text": "Security Considerations", "status": "Anchor not found: #security-considerations"}, {"file": "140-docker-deployment.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "060-database-optimization.md", "link": "#backup--recovery", "text": "Backup & Recovery", "status": "Anchor not found: #backup--recovery"}, {"file": "060-database-optimization.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "020-server-configuration.md", "link": "#ssl-tls-configuration", "text": "SSL/TLS Configuration", "status": "Anchor not found: #ssl-tls-configuration"}, {"file": "120-maintenance-procedures.md", "link": "#performance-optimization", "text": "Performance Optimization", "status": "Anchor not found: #performance-optimization"}, {"file": "120-maintenance-procedures.md", "link": "#security-maintenance", "text": "Security Maintenance", "status": "Anchor not found: #security-maintenance"}, {"file": "120-maintenance-procedures.md", "link": "#monitoring--health-checks", "text": "Monitoring & Health Checks", "status": "Anchor not found: #monitoring--health-checks"}, {"file": "120-maintenance-procedures.md", "link": "#emergency-procedures", "text": "Emergency Procedures", "status": "Anchor not found: #emergency-procedures"}, {"file": "120-maintenance-procedures.md", "link": "#troubleshooting", "text": "Troubleshooting", "status": "Anchor not found: #troubleshooting"}, {"file": "000-index.md", "link": "#monitoring--maintenance", "text": "Monitoring & Maintenance", "status": "Anchor not found: #monitoring--maintenance"}, {"file": "000-index.md", "link": "150-cloud-deployment.md", "text": "Cloud Deployment", "status": "File not found: 150-cloud-deployment.md"}, {"file": "000-index.md", "link": "../setup/000-index.md", "text": "Filament Setup Guide", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../setup/000-index.md"}, {"file": "000-index.md", "link": "../resources/000-index.md", "text": "Filament Resources", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../resources/000-index.md"}, {"file": "000-index.md", "link": "../features/000-index.md", "text": "Filament Features", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../features/000-index.md"}, {"file": "000-index.md", "link": "../testing/000-index.md", "text": "Testing Documentation", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../testing/000-index.md"}, {"file": "000-index.md", "link": "../README.md", "text": "Filament Documentation Index", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../README.md"}, {"file": "080-caching-strategy.md", "link": "#session--user-caching", "text": "Session & User Caching", "status": "Anchor not found: #session--user-caching"}, {"file": "080-caching-strategy.md", "link": "#file--asset-caching", "text": "File & Asset Caching", "status": "Anchor not found: #file--asset-caching"}, {"file": "080-caching-strategy.md", "link": "#monitoring--optimization", "text": "Monitoring & Optimization", "status": "Anchor not found: #monitoring--optimization"}], "resolved_issues": [], "status": "PASS", "execution_time": 0.05324268341064453}, "detailed_results": {"040-ssl-configuration.md": {"file": "040-ssl-configuration.md", "total_links": 14, "internal_links": 2, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Let's Encrypt Setup", "url": "#lets-encrypt-setup", "type": "anchor", "status": "Anchor not found: #lets-encrypt-setup"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "SSL Certificate Types", "url": "#ssl-certificate-types", "type": "anchor", "status": "Anchor found: SSL Certificate Types"}, {"text": "Commercial Certificate Setup", "url": "#commercial-certificate-setup", "type": "anchor", "status": "Anchor found: Commercial Certificate Setup"}, {"text": "Nginx SSL Configuration", "url": "#nginx-ssl-configuration", "type": "anchor", "status": "Anchor found: Nginx SSL Configuration"}, {"text": "Apache SSL Configuration", "url": "#apache-ssl-configuration", "type": "anchor", "status": "Anchor found: Apache SSL Configuration"}, {"text": "SSL Security Best Practices", "url": "#ssl-security-best-practices", "type": "anchor", "status": "Anchor found: SSL Security Best Practices"}, {"text": "Certificate Management", "url": "#certificate-management", "type": "anchor", "status": "Anchor found: Certificate Management"}, {"text": "Automated Renewal", "url": "#automated-renewal", "type": "anchor", "status": "Anchor found: Automated Renewal"}, {"text": "SSL Testing and Validation", "url": "#ssl-testing-and-validation", "type": "anchor", "status": "Anchor found: SSL Testing and Validation"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Monitoring and Alerts", "url": "#monitoring-and-alerts", "type": "anchor", "status": "Anchor found: Monitoring and Alerts"}, {"text": "Security Hardening", "url": "030-security-hardening.md", "type": "internal", "status": "File exists: 030-security-hardening.md"}, {"text": "Performance Optimization", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}]}, "100-logging-configuration.md": {"file": "100-logging-configuration.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Log Channels & Drivers", "url": "#log-channels--drivers", "type": "anchor", "status": "Anchor not found: #log-channels--drivers"}, {"text": "Log Rotation & Retention", "url": "#log-rotation--retention", "type": "anchor", "status": "Anchor not found: #log-rotation--retention"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Laravel Logging Configuration", "url": "#laravel-logging-configuration", "type": "anchor", "status": "Anchor found: <PERSON><PERSON> Logging Configuration"}, {"text": "Structured Logging", "url": "#structured-logging", "type": "anchor", "status": "Anchor found: Structured Logging"}, {"text": "Centralized Logging", "url": "#centralized-logging", "type": "anchor", "status": "Anchor found: Centralized Logging"}, {"text": "Security Logging", "url": "#security-logging", "type": "anchor", "status": "Anchor found: Security Logging"}, {"text": "Performance Logging", "url": "#performance-logging", "type": "anchor", "status": "Anchor found: Performance Logging"}, {"text": "Error Tracking", "url": "#error-tracking", "type": "anchor", "status": "Anchor found: <PERSON><PERSON><PERSON>ing"}, {"text": "Log Analysis", "url": "#log-analysis", "type": "anchor", "status": "Anchor found: Log Analysis"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Backup Strategy", "url": "110-backup-strategy.md", "type": "internal", "status": "File exists: 110-backup-strategy.md"}, {"text": "Maintenance Procedures", "url": "120-maintenance-procedures.md", "type": "internal", "status": "File exists: 120-maintenance-procedures.md"}, {"text": "CI/CD Pipeline", "url": "130-cicd-pipeline.md", "type": "internal", "status": "File exists: 130-cicd-pipeline.md"}]}, "030-security-hardening.md": {"file": "030-security-hardening.md", "total_links": 16, "internal_links": 2, "anchor_links": 14, "external_links": 0, "broken_links": [{"text": "CSRF Protection", "url": "#csrf-protection", "type": "anchor", "status": "Anchor not found: #csrf-protection"}, {"text": "Input Validation", "url": "#input-validation", "type": "anchor", "status": "Anchor not found: #input-validation"}, {"text": "Monitoring and Logging", "url": "#monitoring-and-logging", "type": "anchor", "status": "Anchor not found: #monitoring-and-logging"}, {"text": "Security Auditing", "url": "#security-auditing", "type": "anchor", "status": "Anchor not found: #security-auditing"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Server Security", "url": "#server-security", "type": "anchor", "status": "Anchor found: Server Security"}, {"text": "Application Security", "url": "#application-security", "type": "anchor", "status": "Anchor found: Application Security"}, {"text": "Database Security", "url": "#database-security", "type": "anchor", "status": "Anchor found: Database security"}, {"text": "File System Security", "url": "#file-system-security", "type": "anchor", "status": "Anchor found: File System Security"}, {"text": "Network Security", "url": "#network-security", "type": "anchor", "status": "Anchor found: Network Security"}, {"text": "Authentication Security", "url": "#authentication-security", "type": "anchor", "status": "Anchor found: Authentication Security"}, {"text": "Session Security", "url": "#session-security", "type": "anchor", "status": "Anchor found: Session security"}, {"text": "Security Headers", "url": "#security-headers", "type": "anchor", "status": "Anchor found: Security Headers"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Server Configuration", "url": "020-server-configuration.md", "type": "internal", "status": "File exists: 020-server-configuration.md"}, {"text": "SSL Configuration", "url": "040-ssl-configuration.md", "type": "internal", "status": "File exists: 040-ssl-configuration.md"}]}, "110-backup-strategy.md": {"file": "110-backup-strategy.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Backup Architecture", "url": "#backup-architecture", "type": "anchor", "status": "Anchor found: Backup Architecture"}, {"text": "Database Backup Strategy", "url": "#database-backup-strategy", "type": "anchor", "status": "Anchor found: Database Backup Strategy"}, {"text": "File System Backup", "url": "#file-system-backup", "type": "anchor", "status": "Anchor found: File System Backup"}, {"text": "<PERSON><PERSON> Backup Package", "url": "#laravel-backup-package", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>up Package"}, {"text": "Automated Backup Scheduling", "url": "#automated-backup-scheduling", "type": "anchor", "status": "Anchor found: Automated Backup Scheduling"}, {"text": "Backup Verification", "url": "#backup-verification", "type": "anchor", "status": "Anchor found: Backup Verification"}, {"text": "Disaster Recovery", "url": "#disaster-recovery", "type": "anchor", "status": "Anchor found: Disaster Recovery"}, {"text": "Backup Monitoring", "url": "#backup-monitoring", "type": "anchor", "status": "Anchor found: Backup Monitoring"}, {"text": "Cloud Storage Integration", "url": "#cloud-storage-integration", "type": "anchor", "status": "Anchor found: Cloud Storage Integration"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Maintenance Procedures", "url": "120-maintenance-procedures.md", "type": "internal", "status": "File exists: 120-maintenance-procedures.md"}, {"text": "CI/CD Pipeline", "url": "130-cicd-pipeline.md", "type": "internal", "status": "File exists: 130-cicd-pipeline.md"}, {"text": "Docker Deployment", "url": "140-docker-deployment.md", "type": "internal", "status": "File exists: 140-docker-deployment.md"}]}, "130-cicd-pipeline.md": {"file": "130-cicd-pipeline.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Build & Deployment", "url": "#build--deployment", "type": "anchor", "status": "Anchor not found: #build--deployment"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Pipeline Architecture", "url": "#pipeline-architecture", "type": "anchor", "status": "Anchor found: Pipeline Architecture"}, {"text": "GitHub Actions Workflow", "url": "#github-actions-workflow", "type": "anchor", "status": "Anchor found: GitHub Actions Workflow"}, {"text": "Testing Pipeline", "url": "#testing-pipeline", "type": "anchor", "status": "Anchor found: Testing Pipeline"}, {"text": "Quality Gates", "url": "#quality-gates", "type": "anchor", "status": "Anchor found: Quality Gates"}, {"text": "Environment Management", "url": "#environment-management", "type": "anchor", "status": "Anchor found: Environment Management"}, {"text": "Security Scanning", "url": "#security-scanning", "type": "anchor", "status": "Anchor found: Security Scanning"}, {"text": "Monitoring Integration", "url": "#monitoring-integration", "type": "anchor", "status": "Anchor found: Monitoring Integration"}, {"text": "Rollback Procedures", "url": "#rollback-procedures", "type": "anchor", "status": "Anchor found: Rollback Procedures"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Docker Deployment", "url": "140-docker-deployment.md", "type": "internal", "status": "File exists: 140-docker-deployment.md"}, {"text": "Scaling Strategies", "url": "160-scaling-strategies.md", "type": "internal", "status": "File exists: 160-scaling-strategies.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}]}, "160-scaling-strategies.md": {"file": "160-scaling-strategies.md", "total_links": 12, "internal_links": 0, "anchor_links": 12, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Scaling Architecture", "url": "#scaling-architecture", "type": "anchor", "status": "Anchor found: Scaling Architecture"}, {"text": "Horizontal Scaling", "url": "#horizontal-scaling", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>"}, {"text": "Vertical Scaling", "url": "#vertical-scaling", "type": "anchor", "status": "Anchor found: <PERSON>ert<PERSON>"}, {"text": "<PERSON><PERSON>", "url": "#load-balancing", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>"}, {"text": "Database Scaling", "url": "#database-scaling", "type": "anchor", "status": "Anchor found: <PERSON> Scaling"}, {"text": "Caching Strategies", "url": "#caching-strategies", "type": "anchor", "status": "Anchor found: Caching Strategies"}, {"text": "<PERSON><PERSON>", "url": "#queue-scaling", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>"}, {"text": "CDN Integration", "url": "#cdn-integration", "type": "anchor", "status": "Anchor found: CDN Integration"}, {"text": "Performance Monitoring", "url": "#performance-monitoring", "type": "anchor", "status": "Anchor found: Performance Monitoring"}, {"text": "Auto-Scaling", "url": "#auto-scaling", "type": "anchor", "status": "Anchor found: Auto-Scaling"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}]}, "150-performance-optimization-guide.md": {"file": "150-performance-optimization-guide.md", "total_links": 12, "internal_links": 3, "anchor_links": 9, "external_links": 0, "broken_links": [{"text": "Monitoring & Profiling", "url": "#monitoring--profiling", "type": "anchor", "status": "Anchor not found: #monitoring--profiling"}, {"text": "Browser Testing Guide", "url": "140-browser-testing.md", "type": "internal", "status": "File not found: 140-browser-testing.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Database Optimization", "url": "#database-optimization", "type": "anchor", "status": "Anchor found: Database Optimization"}, {"text": "Caching Strategies", "url": "#caching-strategies", "type": "anchor", "status": "Anchor found: Caching Strategies"}, {"text": "Asset Optimization", "url": "#asset-optimization", "type": "anchor", "status": "Anchor found: Asset Optimization"}, {"text": "Query Optimization", "url": "#query-optimization", "type": "anchor", "status": "Anchor found: Query Optimization"}, {"text": "Memory Management", "url": "#memory-management", "type": "anchor", "status": "Anchor found: Memory Management"}, {"text": "Production Checklist", "url": "#production-checklist", "type": "anchor", "status": "Anchor found: Production Checklist"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Scaling Strategies Guide", "url": "160-scaling-strategies.md", "type": "internal", "status": "File exists: 160-scaling-strategies.md"}, {"text": "Deployment Index", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}]}, "090-monitoring-setup.md": {"file": "090-monitoring-setup.md", "total_links": 13, "internal_links": 3, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Error Tracking & Logging", "url": "#error-tracking--logging", "type": "anchor", "status": "Anchor not found: #error-tracking--logging"}, {"text": "Health Checks & Uptime Monitoring", "url": "#health-checks--uptime-monitoring", "type": "anchor", "status": "Anchor not found: #health-checks--uptime-monitoring"}, {"text": "Alerting & Notifications", "url": "#alerting--notifications", "type": "anchor", "status": "Anchor not found: #alerting--notifications"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Application Performance Monitoring", "url": "#application-performance-monitoring", "type": "anchor", "status": "Anchor found: Application Performance Monitoring"}, {"text": "Database Monitoring", "url": "#database-monitoring", "type": "anchor", "status": "Anchor found: Database Monitoring"}, {"text": "Queue Monitoring", "url": "#queue-monitoring", "type": "anchor", "status": "Anchor found: Queue Monitoring"}, {"text": "Security Monitoring", "url": "#security-monitoring", "type": "anchor", "status": "Anchor found: Security Monitoring"}, {"text": "Dashboard Setup", "url": "#dashboard-setup", "type": "anchor", "status": "Anchor found: Dashboard Setup"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Logging Configuration", "url": "100-logging-configuration.md", "type": "internal", "status": "File exists: 100-logging-configuration.md"}, {"text": "Backup Strategy", "url": "110-backup-strategy.md", "type": "internal", "status": "File exists: 110-backup-strategy.md"}, {"text": "Maintenance Procedures", "url": "120-maintenance-procedures.md", "type": "internal", "status": "File exists: 120-maintenance-procedures.md"}]}, "README.md": {"file": "README.md", "total_links": 20, "internal_links": 20, "anchor_links": 0, "external_links": 0, "broken_links": [{"text": "Cloud Deployment", "url": "150-cloud-deployment.md", "type": "internal", "status": "File not found: 150-cloud-deployment.md"}, {"text": "Setup Documentation", "url": "../setup/", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../setup"}, {"text": "Resources Documentation", "url": "../resources/", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../resources"}, {"text": "Features Documentation", "url": "../features/", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../features"}, {"text": "Testing Documentation", "url": "../testing/", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../testing"}], "working_links": [{"text": "Production Environment", "url": "010-production-environment.md", "type": "internal", "status": "File exists: 010-production-environment.md"}, {"text": "Server Configuration", "url": "020-server-configuration.md", "type": "internal", "status": "File exists: 020-server-configuration.md"}, {"text": "Security Hardening", "url": "030-security-hardening.md", "type": "internal", "status": "File exists: 030-security-hardening.md"}, {"text": "SSL Configuration", "url": "040-ssl-configuration.md", "type": "internal", "status": "File exists: 040-ssl-configuration.md"}, {"text": "Performance Optimization", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}, {"text": "Database Optimization", "url": "060-database-optimization.md", "type": "internal", "status": "File exists: 060-database-optimization.md"}, {"text": "Asset Optimization", "url": "070-asset-optimization.md", "type": "internal", "status": "File exists: 070-asset-optimization.md"}, {"text": "Caching Strategy", "url": "080-caching-strategy.md", "type": "internal", "status": "File exists: 080-caching-strategy.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}, {"text": "Logging Configuration", "url": "100-logging-configuration.md", "type": "internal", "status": "File exists: 100-logging-configuration.md"}, {"text": "Backup Strategy", "url": "110-backup-strategy.md", "type": "internal", "status": "File exists: 110-backup-strategy.md"}, {"text": "Maintenance Procedures", "url": "120-maintenance-procedures.md", "type": "internal", "status": "File exists: 120-maintenance-procedures.md"}, {"text": "CI/CD Pipeline", "url": "130-cicd-pipeline.md", "type": "internal", "status": "File exists: 130-cicd-pipeline.md"}, {"text": "Docker Deployment", "url": "140-docker-deployment.md", "type": "internal", "status": "File exists: 140-docker-deployment.md"}, {"text": "Scaling Strategies", "url": "160-scaling-strategies.md", "type": "internal", "status": "File exists: 160-scaling-strategies.md"}]}, "140-docker-deployment.md": {"file": "140-docker-deployment.md", "total_links": 11, "internal_links": 0, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Multi-Stage Builds", "url": "#multi-stage-builds", "type": "anchor", "status": "Anchor not found: #multi-stage-builds"}, {"text": "Production Configuration", "url": "#production-configuration", "type": "anchor", "status": "Anchor not found: #production-configuration"}, {"text": "Kubernetes Deployment", "url": "#kubernetes-deployment", "type": "anchor", "status": "Anchor not found: #kubernetes-deployment"}, {"text": "Container Orchestration", "url": "#container-orchestration", "type": "anchor", "status": "Anchor not found: #container-orchestration"}, {"text": "Monitoring & Logging", "url": "#monitoring--logging", "type": "anchor", "status": "Anchor not found: #monitoring--logging"}, {"text": "Security Considerations", "url": "#security-considerations", "type": "anchor", "status": "Anchor not found: #security-considerations"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor not found: #troubleshooting"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Docker Architecture", "url": "#docker-architecture", "type": "anchor", "status": "Anchor found: Docker Architecture"}, {"text": "Dockerfile Configuration", "url": "#dockerfile-configuration", "type": "anchor", "status": "Anchor found: Dockerfile Configuration"}, {"text": "<PERSON><PERSON> <PERSON><PERSON>", "url": "#docker-compose-setup", "type": "anchor", "status": "Anchor found: <PERSON><PERSON>"}]}, "060-database-optimization.md": {"file": "060-database-optimization.md", "total_links": 12, "internal_links": 3, "anchor_links": 9, "external_links": 0, "broken_links": [{"text": "Backup & Recovery", "url": "#backup--recovery", "type": "anchor", "status": "Anchor not found: #backup--recovery"}, {"text": "Monitoring & Maintenance", "url": "#monitoring--maintenance", "type": "anchor", "status": "Anchor not found: #monitoring--maintenance"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Index Strategy", "url": "#index-strategy", "type": "anchor", "status": "Anchor found: Index Strategy"}, {"text": "Query Optimization", "url": "#query-optimization", "type": "anchor", "status": "Anchor found: Query Optimization"}, {"text": "Connection Management", "url": "#connection-management", "type": "anchor", "status": "Anchor found: Connection Management"}, {"text": "Partitioning Strategies", "url": "#partitioning-strategies", "type": "anchor", "status": "Anchor found: Partitioning Strategies"}, {"text": "Performance Tuning", "url": "#performance-tuning", "type": "anchor", "status": "Anchor found: Performance Tuning"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Performance Optimization Guide", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}, {"text": "Asset Optimization Guide", "url": "070-asset-optimization.md", "type": "internal", "status": "File exists: 070-asset-optimization.md"}, {"text": "Deployment Index", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}]}, "050-performance-optimization.md": {"file": "050-performance-optimization.md", "total_links": 11, "internal_links": 4, "anchor_links": 7, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "SQLite Performance Optimization", "url": "#sqlite-performance-optimization", "type": "anchor", "status": "Anchor found: SQLite Performance Optimization"}, {"text": "Application Caching", "url": "#application-caching", "type": "anchor", "status": "Anchor found: Application Caching"}, {"text": "PHP Performance Tuning", "url": "#php-performance-tuning", "type": "anchor", "status": "Anchor found: PHP Performance Tuning"}, {"text": "Frontend Optimization", "url": "#frontend-optimization", "type": "anchor", "status": "Anchor found: Frontend Optimization"}, {"text": "Query Optimization", "url": "#query-optimization", "type": "anchor", "status": "Anchor found: Query Optimization"}, {"text": "Monitoring and Profiling", "url": "#monitoring-and-profiling", "type": "anchor", "status": "Anchor found: Monitoring and Profiling"}, {"text": "Production Environment", "url": "010-production-environment.md", "type": "internal", "status": "File exists: 010-production-environment.md"}, {"text": "Caching Strategy", "url": "080-caching-strategy.md", "type": "internal", "status": "File exists: 080-caching-strategy.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}, {"text": "Database Optimization", "url": "060-database-optimization.md", "type": "internal", "status": "File exists: 060-database-optimization.md"}]}, "020-server-configuration.md": {"file": "020-server-configuration.md", "total_links": 18, "internal_links": 4, "anchor_links": 11, "external_links": 3, "broken_links": [{"text": "SSL/TLS Configuration", "url": "#ssl-tls-configuration", "type": "anchor", "status": "Anchor not found: #ssl-tls-configuration"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Server Requirements", "url": "#server-requirements", "type": "anchor", "status": "Anchor found: Server Requirements"}, {"text": "Web Server Configuration", "url": "#web-server-configuration", "type": "anchor", "status": "Anchor found: Web Server Configuration"}, {"text": "PHP Configuration", "url": "#php-configuration", "type": "anchor", "status": "Anchor found: PHP Configuration"}, {"text": "Database Configuration", "url": "#database-configuration", "type": "anchor", "status": "Anchor found: Database Configuration"}, {"text": "Security Hardening", "url": "#security-hardening", "type": "anchor", "status": "Anchor found: Security Hardening"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Monitoring Setup", "url": "#monitoring-setup", "type": "anchor", "status": "Anchor found: Monitoring Setup"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor found: Troubleshooting"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Production Environment", "url": "010-production-environment.md", "type": "internal", "status": "File exists: 010-production-environment.md"}, {"text": "Security Hardening", "url": "030-security-hardening.md", "type": "internal", "status": "File exists: 030-security-hardening.md"}, {"text": "Performance Optimization", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}, {"text": "Nginx Documentation", "url": "https://nginx.org/en/docs/", "type": "external", "status": "External link (not validated)"}, {"text": "PHP-FPM Configuration", "url": "https://www.php.net/manual/en/install.fpm.configuration.php", "type": "external", "status": "External link (not validated)"}, {"text": "Let's Encrypt", "url": "https://letsencrypt.org/", "type": "external", "status": "External link (not validated)"}]}, "070-asset-optimization.md": {"file": "070-asset-optimization.md", "total_links": 12, "internal_links": 3, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Vite Configuration", "url": "#vite-configuration", "type": "anchor", "status": "Anchor found: Vite Configuration"}, {"text": "CSS Optimization", "url": "#css-optimization", "type": "anchor", "status": "Anchor found: CSS Optimization"}, {"text": "JavaScript Optimization", "url": "#javascript-optimization", "type": "anchor", "status": "Anchor found: JavaScript Optimization"}, {"text": "Image Optimization", "url": "#image-optimization", "type": "anchor", "status": "Anchor found: Image Optimization"}, {"text": "Font Optimization", "url": "#font-optimization", "type": "anchor", "status": "Anchor found: Font Optimization"}, {"text": "CDN Integration", "url": "#cdn-integration", "type": "anchor", "status": "Anchor found: CDN Integration"}, {"text": "Performance Monitoring", "url": "#performance-monitoring", "type": "anchor", "status": "Anchor found: Performance Monitoring"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Database Optimization Guide", "url": "060-database-optimization.md", "type": "internal", "status": "File exists: 060-database-optimization.md"}, {"text": "Caching Strategy Guide", "url": "080-caching-strategy.md", "type": "internal", "status": "File exists: 080-caching-strategy.md"}, {"text": "Deployment Index", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}]}, "010-production-environment.md": {"file": "010-production-environment.md", "total_links": 11, "internal_links": 4, "anchor_links": 7, "external_links": 0, "broken_links": [], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Server Requirements", "url": "#server-requirements", "type": "anchor", "status": "Anchor found: Server Requirements"}, {"text": "Environment Configuration", "url": "#environment-configuration", "type": "anchor", "status": "Anchor found: Environment Configuration"}, {"text": "SQLite Production Setup", "url": "#sqlite-production-setup", "type": "anchor", "status": "Anchor found: SQLite Production Setup"}, {"text": "PHP Configuration", "url": "#php-configuration", "type": "anchor", "status": "Anchor found: PHP Configuration"}, {"text": "Web Server Configuration", "url": "#web-server-configuration", "type": "anchor", "status": "Anchor found: Web Server Configuration"}, {"text": "Security Configuration", "url": "#security-configuration", "type": "anchor", "status": "Anchor found: Security Configuration"}, {"text": "Server Configuration", "url": "020-server-configuration.md", "type": "internal", "status": "File exists: 020-server-configuration.md"}, {"text": "Security Hardening", "url": "030-security-hardening.md", "type": "internal", "status": "File exists: 030-security-hardening.md"}, {"text": "Performance Optimization", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}]}, "120-maintenance-procedures.md": {"file": "120-maintenance-procedures.md", "total_links": 11, "internal_links": 0, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor not found: #performance-optimization"}, {"text": "Security Maintenance", "url": "#security-maintenance", "type": "anchor", "status": "Anchor not found: #security-maintenance"}, {"text": "Monitoring & Health Checks", "url": "#monitoring--health-checks", "type": "anchor", "status": "Anchor not found: #monitoring--health-checks"}, {"text": "Emergency Procedures", "url": "#emergency-procedures", "type": "anchor", "status": "Anchor not found: #emergency-procedures"}, {"text": "Troubleshooting", "url": "#troubleshooting", "type": "anchor", "status": "Anchor not found: #troubleshooting"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Routine Maintenance Schedule", "url": "#routine-maintenance-schedule", "type": "anchor", "status": "Anchor found: Routine Maintenance Schedule"}, {"text": "System Updates", "url": "#system-updates", "type": "anchor", "status": "Anchor found: System Updates"}, {"text": "Database Maintenance", "url": "#database-maintenance", "type": "anchor", "status": "Anchor found: Database maintenance"}, {"text": "Cache Management", "url": "#cache-management", "type": "anchor", "status": "Anchor found: Cache Management"}, {"text": "Log Management", "url": "#log-management", "type": "anchor", "status": "Anchor found: Log Management"}]}, "000-index.md": {"file": "000-index.md", "total_links": 32, "internal_links": 22, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Monitoring & Maintenance", "url": "#monitoring--maintenance", "type": "anchor", "status": "Anchor not found: #monitoring--maintenance"}, {"text": "Cloud Deployment", "url": "150-cloud-deployment.md", "type": "internal", "status": "File not found: 150-cloud-deployment.md"}, {"text": "Filament Setup Guide", "url": "../setup/000-index.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../setup/000-index.md"}, {"text": "Filament Resources", "url": "../resources/000-index.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../resources/000-index.md"}, {"text": "Filament Features", "url": "../features/000-index.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../features/000-index.md"}, {"text": "Testing Documentation", "url": "../testing/000-index.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../testing/000-index.md"}, {"text": "Filament Documentation Index", "url": "../README.md", "type": "internal", "status": "Path outside base directory: .ai/guides/chinook/filament/deployment/../README.md"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Documentation Structure", "url": "#documentation-structure", "type": "anchor", "status": "Anchor found: Documentation Structure"}, {"text": "Production Setup", "url": "#production-setup", "type": "anchor", "status": "Anchor found: Production Setup"}, {"text": "Performance Optimization", "url": "#performance-optimization", "type": "anchor", "status": "Anchor found: Performance Optimization"}, {"text": "Deployment Automation", "url": "#deployment-automation", "type": "anchor", "status": "Anchor found: Deployment Automation"}, {"text": "Quick Start Guide", "url": "#quick-start-guide", "type": "anchor", "status": "Anchor found: Quick Start Guide"}, {"text": "Architecture Overview", "url": "#architecture-overview", "type": "anchor", "status": "Anchor found: Architecture Overview"}, {"text": "Best Practices", "url": "#best-practices", "type": "anchor", "status": "Anchor found: Best Practices"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Production Environment", "url": "010-production-environment.md", "type": "internal", "status": "File exists: 010-production-environment.md"}, {"text": "Server Configuration", "url": "020-server-configuration.md", "type": "internal", "status": "File exists: 020-server-configuration.md"}, {"text": "Security Hardening", "url": "030-security-hardening.md", "type": "internal", "status": "File exists: 030-security-hardening.md"}, {"text": "SSL Configuration", "url": "040-ssl-configuration.md", "type": "internal", "status": "File exists: 040-ssl-configuration.md"}, {"text": "Performance Optimization", "url": "050-performance-optimization.md", "type": "internal", "status": "File exists: 050-performance-optimization.md"}, {"text": "Database Optimization", "url": "060-database-optimization.md", "type": "internal", "status": "File exists: 060-database-optimization.md"}, {"text": "Asset Optimization", "url": "070-asset-optimization.md", "type": "internal", "status": "File exists: 070-asset-optimization.md"}, {"text": "Caching Strategy", "url": "080-caching-strategy.md", "type": "internal", "status": "File exists: 080-caching-strategy.md"}, {"text": "Monitoring Setup", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}, {"text": "Logging Configuration", "url": "100-logging-configuration.md", "type": "internal", "status": "File exists: 100-logging-configuration.md"}, {"text": "Backup Strategy", "url": "110-backup-strategy.md", "type": "internal", "status": "File exists: 110-backup-strategy.md"}, {"text": "Maintenance Procedures", "url": "120-maintenance-procedures.md", "type": "internal", "status": "File exists: 120-maintenance-procedures.md"}, {"text": "CI/CD Pipeline", "url": "130-cicd-pipeline.md", "type": "internal", "status": "File exists: 130-cicd-pipeline.md"}, {"text": "Docker Deployment", "url": "140-docker-deployment.md", "type": "internal", "status": "File exists: 140-docker-deployment.md"}, {"text": "Scaling Strategies", "url": "160-scaling-strategies.md", "type": "internal", "status": "File exists: 160-scaling-strategies.md"}, {"text": "Production Environment Setup", "url": "010-production-environment.md", "type": "internal", "status": "File exists: 010-production-environment.md"}]}, "080-caching-strategy.md": {"file": "080-caching-strategy.md", "total_links": 13, "internal_links": 3, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Session & User Caching", "url": "#session--user-caching", "type": "anchor", "status": "Anchor not found: #session--user-caching"}, {"text": "File & Asset Caching", "url": "#file--asset-caching", "type": "anchor", "status": "Anchor not found: #file--asset-caching"}, {"text": "Monitoring & Optimization", "url": "#monitoring--optimization", "type": "anchor", "status": "Anchor not found: #monitoring--optimization"}], "working_links": [{"text": "Overview", "url": "#overview", "type": "anchor", "status": "Anchor found: Overview"}, {"text": "Redis Configuration", "url": "#redis-configuration", "type": "anchor", "status": "Anchor found: Redis Configuration"}, {"text": "Application Caching", "url": "#application-caching", "type": "anchor", "status": "Anchor found: Application Caching"}, {"text": "Database Query Caching", "url": "#database-query-caching", "type": "anchor", "status": "Anchor found: Database Query Caching"}, {"text": "API Response Caching", "url": "#api-response-caching", "type": "anchor", "status": "Anchor found: API Response Caching"}, {"text": "Cache Invalidation", "url": "#cache-invalidation", "type": "anchor", "status": "Anchor found: <PERSON><PERSON> Invalidation"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation"}, {"text": "Asset Optimization Guide", "url": "070-asset-optimization.md", "type": "internal", "status": "File exists: 070-asset-optimization.md"}, {"text": "Monitoring Setup Guide", "url": "090-monitoring-setup.md", "type": "internal", "status": "File exists: 090-monitoring-setup.md"}, {"text": "Deployment Index", "url": "000-index.md", "type": "internal", "status": "File exists: 000-index.md"}]}}}