# Automated Link Validation Report

**Status:** ❌ FAIL
**Timestamp:** 2025-07-08T06:00:22.983851
**Execution Time:** 0.75 seconds

## Summary

- **Total Files:** 164
- **Total Links:** 3100
- **Broken Links:** 400
- **Success Rate:** 87.1%

## Critical Files Status

| File | Broken Links | Status |
|------|--------------|--------|
| 000-chinook-index.md | 19 | ❌ Major Issues |
| 050-chinook-advanced-features-guide.md | 1 | ⚠️ Minor Issues |
| 060-chinook-media-library-guide.md | 0 | ✅ Perfect |
| 070-chinook-hierarchy-comparison-guide.md | 0 | ✅ Perfect |
| filament/setup/000-index.md | 2 | ⚠️ Minor Issues |
| filament/resources/000-index.md | 1 | ⚠️ Minor Issues |
| packages/000-packages-index.md | 19 | ❌ Major Issues |
| testing/000-testing-index.md | 0 | ✅ Perfect |

## New Issues (400)

- **040-chinook-seeders-guide.md:** Customer Seeder → #customer-seeder
- **040-chinook-seeders-guide.md:** Playlist Seeder → #playlist-seeder
- **040-chinook-seeders-guide.md:** Track Seeder → #track-seeder
- **040-chinook-seeders-guide.md:** Invoice Seeder → #invoice-seeder
- **040-chinook-seeders-guide.md:** InvoiceLine Seeder → #invoiceline-seeder
- **040-chinook-seeders-guide.md:** PlaylistTrack Seeder → #playlisttrack-seeder
- **040-chinook-seeders-guide.md:** Database Seeder → #database-seeder
- **README.md:** Documentation Audit Report → reports/DOCUMENTATION_AUDIT_REPORT.md
- **000-chinook-index.md:** 8. Panel Setup & Configuration → #8-panel-setup--configuration
- **000-chinook-index.md:** 9. Model Standards & Architecture → #9-model-standards--architecture

*... and 390 more*

## Recommendations

🚨 **Immediate Action Required:** High number of broken links detected.
